package com.tqhit.battery.one.features.emoji.presentation.customize.adapter
import com.tqhit.battery.one.utils.BatteryLogger

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.android.material.card.MaterialCardView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle

/**
 * Adapter for displaying emoji character options in horizontal RecyclerView.
 * 
 * Handles the display of emoji character images with selection states,
 * premium badges, and loading indicators. Optimized for horizontal scrolling
 * with efficient image loading and view recycling.
 * 
 * Key features:
 * - Efficient image loading with Glide
 * - Selection state management
 * - Premium content indicators
 * - Loading state handling
 * - Click event handling
 */
class EmojiOptionAdapter(
    private val onEmojiSelected: (BatteryStyle) -> Unit
) : ListAdapter<BatteryStyle, EmojiOptionAdapter.EmojiOptionViewHolder>(BatteryStyleDiffCallback()) {
    
    companion object {
        private const val TAG = "EmojiOptionAdapter"
    }
    
    private var selectedEmojiId: String = ""
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EmojiOptionViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_emoji_option, parent, false)
        return EmojiOptionViewHolder(view, onEmojiSelected)
    }
    
    override fun onBindViewHolder(holder: EmojiOptionViewHolder, position: Int) {
        val batteryStyle = getItem(position)
        val isSelected = batteryStyle.id == selectedEmojiId
        holder.bind(batteryStyle, isSelected)
    }
    
    /**
     * Updates the selected emoji ID and refreshes selection states.
     */
    fun setSelectedEmojiId(emojiId: String) {
        if (selectedEmojiId != emojiId) {
            val oldSelectedId = selectedEmojiId
            selectedEmojiId = emojiId
            
            // Refresh items that changed selection state
            currentList.forEachIndexed { index, batteryStyle ->
                if (batteryStyle.id == oldSelectedId || batteryStyle.id == emojiId) {
                    notifyItemChanged(index)
                }
            }
            
            BatteryLogger.d(TAG, "SELECTION: Updated selected emoji ID to: $emojiId")
        }
    }
    
    /**
     * ViewHolder for emoji option items.
     */
    class EmojiOptionViewHolder(
        itemView: View,
        private val onEmojiSelected: (BatteryStyle) -> Unit
    ) : RecyclerView.ViewHolder(itemView) {
        
        private val cardView: MaterialCardView = itemView as MaterialCardView
        private val emojiImageView: ImageView = itemView.findViewById(R.id.emojiImageView)
        private val selectionIndicator: ImageView = itemView.findViewById(R.id.selectionIndicator)
        private val premiumBadge: ImageView = itemView.findViewById(R.id.premiumBadge)
        private val loadingIndicator: ProgressBar = itemView.findViewById(R.id.loadingIndicator)
        
        private var currentBatteryStyle: BatteryStyle? = null
        
        init {
            itemView.setOnClickListener {
                currentBatteryStyle?.let { batteryStyle ->
                    onEmojiSelected(batteryStyle)
                    BatteryLogger.d(TAG, "CLICK: Emoji selected: ${batteryStyle.name}")
                }
            }
        }
        
        fun bind(batteryStyle: BatteryStyle, isSelected: Boolean) {
            currentBatteryStyle = batteryStyle
            
            // Update selection state
            updateSelectionState(isSelected)
            
            // Show premium badge if needed
            premiumBadge.visibility = if (batteryStyle.isPremium) View.VISIBLE else View.GONE
            
            // Load emoji image
            loadEmojiImage(batteryStyle)
            
            // Update content description for accessibility
            itemView.contentDescription = buildString {
                append("Emoji character: ${batteryStyle.name}")
                if (batteryStyle.isPremium) append(", Premium")
                if (isSelected) append(", Selected")
            }
        }
        
        /**
         * Updates the visual selection state of the item.
         */
        private fun updateSelectionState(isSelected: Boolean) {
            selectionIndicator.visibility = if (isSelected) View.VISIBLE else View.GONE
            
            // Update card appearance for selection
            cardView.apply {
                strokeWidth = if (isSelected) {
                    resources.getDimensionPixelSize(R.dimen.selection_stroke_width)
                } else {
                    resources.getDimensionPixelSize(R.dimen.default_stroke_width)
                }
                
                strokeColor = if (isSelected) {
                    context.getColor(R.color.selection_stroke_color)
                } else {
                    context.getColor(R.color.default_stroke_color)
                }
            }
        }
        
        /**
         * Loads the emoji character image using Glide.
         */
        private fun loadEmojiImage(batteryStyle: BatteryStyle) {
            if (batteryStyle.emojiImageUrl.isBlank()) {
                // Show placeholder for empty URL
                emojiImageView.setImageResource(R.drawable.ic_emoji_placeholder)
                loadingIndicator.visibility = View.GONE
                return
            }
            
            // Show loading indicator
            loadingIndicator.visibility = View.VISIBLE
            emojiImageView.visibility = View.GONE
            
            Glide.with(itemView.context)
                .load(batteryStyle.emojiImageUrl)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .placeholder(R.drawable.ic_emoji_placeholder)
                .error(R.drawable.ic_emoji_placeholder)
                .into(object : com.bumptech.glide.request.target.CustomTarget<android.graphics.drawable.Drawable>() {
                    override fun onResourceReady(
                        resource: android.graphics.drawable.Drawable,
                        transition: com.bumptech.glide.request.transition.Transition<in android.graphics.drawable.Drawable>?
                    ) {
                        emojiImageView.setImageDrawable(resource)
                        emojiImageView.visibility = View.VISIBLE
                        loadingIndicator.visibility = View.GONE
                        BatteryLogger.d(TAG, "IMAGE_LOAD: Emoji image loaded for: ${batteryStyle.name}")
                    }
                    
                    override fun onLoadCleared(placeholder: android.graphics.drawable.Drawable?) {
                        emojiImageView.setImageDrawable(placeholder)
                        emojiImageView.visibility = View.VISIBLE
                        loadingIndicator.visibility = View.GONE
                    }
                    
                    override fun onLoadFailed(errorDrawable: android.graphics.drawable.Drawable?) {
                        emojiImageView.setImageDrawable(errorDrawable)
                        emojiImageView.visibility = View.VISIBLE
                        loadingIndicator.visibility = View.GONE
                        BatteryLogger.w(TAG, "IMAGE_LOAD: Failed to load emoji image for: ${batteryStyle.name}")
                    }
                })
        }
    }
    
    /**
     * DiffUtil callback for efficient list updates.
     */
    private class BatteryStyleDiffCallback : DiffUtil.ItemCallback<BatteryStyle>() {
        override fun areItemsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
            return oldItem == newItem
        }
    }
}
