package com.tqhit.battery.one.features.emoji.presentation.customize

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase
import com.tqhit.battery.one.features.emoji.presentation.customize.handler.ConfigurationHandler
import com.tqhit.battery.one.features.emoji.presentation.customize.handler.SaveApplyHandler
import com.tqhit.battery.one.features.emoji.presentation.customize.handler.StyleSelectionHandler
import com.tqhit.battery.one.features.emoji.presentation.customize.handler.UIInteractionHandler
import com.tqhit.battery.one.features.emoji.presentation.customize.manager.DataManager
import com.tqhit.battery.one.features.emoji.presentation.customize.manager.PreviewManager
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * Refactored ViewModel for the customization screen.
 *
 * Now acts as a coordinator that delegates to specialized handler classes and managers.
 * This follows the stats module architecture pattern with comprehensive logging.
 *
 * Key responsibilities:
 * - Coordinate between specialized handlers and managers
 * - Manage UI state flow
 * - Delegate events to appropriate handlers
 * - Provide reactive data streams for UI updates
 */
@HiltViewModel
class CustomizeViewModel @Inject constructor(
    private val styleSelectionHandler: StyleSelectionHandler,
    private val configurationHandler: ConfigurationHandler,
    private val saveApplyHandler: SaveApplyHandler,
    private val uiInteractionHandler: UIInteractionHandler,
    private val dataManager: DataManager,
    private val previewManager: PreviewManager
) : ViewModel() {

    companion object {
        private const val TAG = "EmojiCustomize"
    }

    // Internal mutable state
    private val _uiState = MutableStateFlow(CustomizeState.createInitial())

    // Public state exposure
    val uiState: StateFlow<CustomizeState> = _uiState.asStateFlow()
    
    init {
        BatteryLogger.d(TAG, "EMOJI_INIT: CustomizeViewModel initialized")
        startDataObservation()
        loadInitialData()
    }

    /**
     * Handles user events from the UI by delegating to appropriate handlers.
     */
    fun handleEvent(event: CustomizeEvent) {
        BatteryLogger.d(TAG, "EMOJI_EVENT: Handling event: ${event::class.simpleName}")

        when (event) {
            // Lifecycle Events
            is CustomizeEvent.OnScreenEnter -> uiInteractionHandler.handleScreenEnter { loadInitialData() }
            is CustomizeEvent.OnScreenExit -> uiInteractionHandler.handleScreenExit(_uiState, viewModelScope) { saveApplyHandler.saveCurrentConfiguration(_uiState, ::handleEvent) }
            is CustomizeEvent.OnResume -> uiInteractionHandler.handleResume { loadInitialData() }
            is CustomizeEvent.OnPause -> uiInteractionHandler.handlePause { previewManager.cancelJobs() }

            // Style Selection Events
            is CustomizeEvent.SelectBatteryStyle -> styleSelectionHandler.handleSelectBatteryStyle(event.style, _uiState, viewModelScope) { schedulePreviewUpdate() }
            is CustomizeEvent.SelectBatteryContainer -> styleSelectionHandler.handleSelectBatteryContainer(event.style, _uiState, { scheduleAutoSave() }, { schedulePreviewUpdate() })
            is CustomizeEvent.SelectEmojiCharacter -> styleSelectionHandler.handleSelectEmojiCharacter(event.style, _uiState, { scheduleAutoSave() }, { schedulePreviewUpdate() })
            is CustomizeEvent.BrowseMoreStyles -> styleSelectionHandler.handleBrowseMoreStyles()

            // Configuration Editing Events
            is CustomizeEvent.ToggleEmojiVisibility -> configurationHandler.handleToggleEmojiVisibility(event.showEmoji, _uiState) { updateStyleConfig(it) }
            is CustomizeEvent.TogglePercentageVisibility -> configurationHandler.handleTogglePercentageVisibility(event.showPercentage, _uiState) { updateStyleConfig(it) }
            is CustomizeEvent.ChangePercentageFontSize -> configurationHandler.handleChangePercentageFontSize(event.sizeDp, _uiState) { updateStyleConfig(it) }
            is CustomizeEvent.ChangeEmojiSize -> configurationHandler.handleChangeEmojiSize(event.scale, _uiState) { updateStyleConfig(it) }
            is CustomizeEvent.ChangePercentageColor -> configurationHandler.handleChangePercentageColor(event.color, _uiState) { updateStyleConfig(it) }
            is CustomizeEvent.ChangeOverlayPosition -> configurationHandler.handleChangeOverlayPosition(event.position, _uiState, viewModelScope)

            // Save and Apply Events
            is CustomizeEvent.SaveConfiguration -> saveApplyHandler.handleSaveConfiguration(_uiState, viewModelScope) { saveApplyHandler.saveCurrentConfiguration(_uiState, ::handleEvent) }
            is CustomizeEvent.ApplyAndEnable -> saveApplyHandler.handleApplyAndEnable(_uiState, viewModelScope, { saveApplyHandler.saveCurrentConfiguration(_uiState, ::handleEvent) }, ::handleEvent)
            is CustomizeEvent.ResetToDefaults -> saveApplyHandler.handleResetToDefaults()
            is CustomizeEvent.ConfirmReset -> saveApplyHandler.handleConfirmReset(_uiState, viewModelScope) { loadInitialData() }
            is CustomizeEvent.DiscardChanges -> saveApplyHandler.handleDiscardChanges { loadInitialData() }

            // UI Interaction Events
            is CustomizeEvent.OpenColorPicker -> uiInteractionHandler.handleOpenColorPicker(_uiState)
            is CustomizeEvent.CloseColorPicker -> uiInteractionHandler.handleCloseColorPicker(_uiState)
            is CustomizeEvent.OpenPositionSelector -> uiInteractionHandler.handleOpenPositionSelector(_uiState)
            is CustomizeEvent.ClosePositionSelector -> uiInteractionHandler.handleClosePositionSelector(_uiState)
            is CustomizeEvent.ToggleLivePreview -> previewManager.handleToggleLivePreview(event.showPreview, _uiState)
            is CustomizeEvent.ChangePreviewBatteryLevel -> previewManager.handleChangePreviewBatteryLevel(event.level, _uiState)
            is CustomizeEvent.RefreshPreview -> previewManager.handleRefreshPreview(_uiState, viewModelScope)

            // Permission and Feature Events
            is CustomizeEvent.RequestPermissions -> uiInteractionHandler.handleRequestPermissions()
            is CustomizeEvent.EnableAccessibilityService -> uiInteractionHandler.handleEnableAccessibilityService()
            is CustomizeEvent.ToggleFeatureEnabled -> uiInteractionHandler.handleToggleFeatureEnabled(event.enabled, _uiState, viewModelScope)

            // Error Handling Events
            is CustomizeEvent.RetryOperation -> uiInteractionHandler.handleRetryOperation(_uiState) { loadInitialData() }
            is CustomizeEvent.DismissError -> uiInteractionHandler.handleDismissError(_uiState)
            is CustomizeEvent.ValidateConfiguration -> uiInteractionHandler.handleValidateConfiguration(_uiState, viewModelScope)

            // Navigation Events
            is CustomizeEvent.NavigateBack -> uiInteractionHandler.handleNavigateBack()
            is CustomizeEvent.NavigateToSettings -> uiInteractionHandler.handleNavigateToSettings()
            is CustomizeEvent.NavigateToHelp -> uiInteractionHandler.handleNavigateToHelp()

            // Data Events
            is CustomizeEvent.CustomizationDataLoaded -> dataManager.handleCustomizationDataLoaded(event.success, _uiState)
            is CustomizeEvent.StylesDataLoaded -> dataManager.handleStylesDataLoaded(event.success, _uiState)
            is CustomizeEvent.ConfigurationSaved -> dataManager.handleConfigurationSaved(_uiState)
            is CustomizeEvent.ConfigurationSaveFailed -> dataManager.handleConfigurationSaveFailed(event.error, _uiState)
            is CustomizeEvent.BatteryLevelChanged -> previewManager.handleBatteryLevelChanged(event.level, event.isCharging, _uiState)

            // Premium Events (Phase 5 placeholders)
            is CustomizeEvent.UnlockPremiumStyle -> handleUnlockPremiumStyle(event.style)
            is CustomizeEvent.WatchAdToUnlock -> handleWatchAdToUnlock(event.style)
            is CustomizeEvent.PurchasePremiumAccess -> handlePurchasePremiumAccess()

            // Backup Events (placeholders)
            is CustomizeEvent.ExportConfiguration -> handleExportConfiguration()
            is CustomizeEvent.ImportConfiguration -> handleImportConfiguration(event.data)
            is CustomizeEvent.ShareConfiguration -> handleShareConfiguration()
        }
    }
    
    /**
     * Starts observing data changes using the DataManager.
     */
    private fun startDataObservation() {
        dataManager.startDataObservation(_uiState, viewModelScope) { level, isCharging ->
            handleEvent(CustomizeEvent.BatteryLevelChanged(level, isCharging))
        }
    }

    /**
     * Loads initial data using the DataManager.
     */
    private fun loadInitialData() {
        dataManager.loadInitialData(_uiState, viewModelScope, ::handleEvent)
    }

    // Utility Methods for Coordination

    /**
     * Updates style configuration using the PreviewManager.
     */
    private fun updateStyleConfig(config: BatteryStyleConfig) {
        previewManager.updateStyleConfig(_uiState, viewModelScope, { config }) { scheduleAutoSave() }
    }

    /**
     * Schedules preview update using the PreviewManager.
     */
    private fun schedulePreviewUpdate() {
        previewManager.schedulePreviewUpdate(_uiState, viewModelScope)
    }

    /**
     * Schedules auto-save using the PreviewManager.
     */
    private fun scheduleAutoSave() {
        previewManager.scheduleAutoSave(_uiState, viewModelScope) {
            saveApplyHandler.saveCurrentConfiguration(_uiState, ::handleEvent)
        }
    }

    // Premium Event Handlers (Phase 5 placeholders)
    private fun handleUnlockPremiumStyle(style: BatteryStyle) {
        BatteryLogger.d(TAG, "EMOJI_PREMIUM: Unlock premium style: ${style.name}")
        // TODO: Implement in Phase 5
    }

    private fun handleWatchAdToUnlock(style: BatteryStyle) {
        BatteryLogger.d(TAG, "EMOJI_PREMIUM: Watch ad to unlock: ${style.name}")
        // TODO: Implement in Phase 5
    }

    private fun handlePurchasePremiumAccess() {
        BatteryLogger.d(TAG, "EMOJI_PREMIUM: Purchase premium access")
        // TODO: Implement in Phase 5
    }

    // Backup Event Handlers (placeholders)
    private fun handleExportConfiguration() {
        BatteryLogger.d(TAG, "EMOJI_BACKUP: Export configuration")
        // TODO: Implement export functionality
    }

    private fun handleImportConfiguration(data: String) {
        BatteryLogger.d(TAG, "EMOJI_BACKUP: Import configuration")
        // TODO: Implement import functionality
    }

    private fun handleShareConfiguration() {
        BatteryLogger.d(TAG, "EMOJI_BACKUP: Share configuration")
        // TODO: Implement share functionality
    }


    override fun onCleared() {
        super.onCleared()
        BatteryLogger.d(TAG, "EMOJI_LIFECYCLE: ViewModel cleared")

        // Cancel all jobs through managers
        previewManager.cancelJobs()
        dataManager.cancelDataLoading()
    }
}
