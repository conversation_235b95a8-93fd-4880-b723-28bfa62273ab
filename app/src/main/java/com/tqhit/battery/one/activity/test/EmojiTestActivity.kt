package com.tqhit.battery.one.activity.test

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ActivityEmojiTestBinding
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * Test Activity for Emoji Feature
 * This activity is used to test the emoji feature independently of the main app
 */
@AndroidEntryPoint
class EmojiTestActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityEmojiTestBinding
    private val emojiViewModel: BatteryGalleryViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        BatteryLogger.d("EmojiTestActivity", "onCreate: Starting emoji test activity")
        
        binding = ActivityEmojiTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
        
        BatteryLogger.d("EmojiTestActivity", "onCreate: Emoji test activity setup complete")
    }
    
    private fun setupUI() {
        BatteryLogger.d("EmojiTestActivity", "setupUI: Setting up UI components")
        
        binding.apply {
            // Set up test buttons
            btnTestEmojiLoad.setOnClickListener {
                BatteryLogger.d("EmojiTestActivity", "btnTestEmojiLoad clicked")
                testEmojiLoad()
            }
            
            btnTestEmojiSelection.setOnClickListener {
                BatteryLogger.d("EmojiTestActivity", "btnTestEmojiSelection clicked")
                testEmojiSelection()
            }
            
            btnTestEmojiDisplay.setOnClickListener {
                BatteryLogger.d("EmojiTestActivity", "btnTestEmojiDisplay clicked")
                testEmojiDisplay()
            }
        }
    }
    
    private fun observeViewModel() {
        BatteryLogger.d("EmojiTestActivity", "observeViewModel: Setting up observers")
        
        lifecycleScope.launch {
            emojiViewModel.uiState.collect { state ->
                BatteryLogger.d("EmojiTestActivity", "UI State updated: $state")
                updateUI(state)
            }
        }
    }
    
    private fun updateUI(state: com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState) {
        binding.apply {
            tvStatus.text = "Status: Loading=${state.isLoading}, Refreshing=${state.isRefreshing}"
            tvSelectedEmoji.text = "Selected Style ID: ${state.selectedStyleId ?: "None"}"
            tvAvailableCount.text = "Available Styles: ${state.displayedStyles.size} (Total: ${state.allStyles.size})"

            if (state.errorMessage != null) {
                tvError.text = "Error: ${state.errorMessage}"
                tvError.visibility = android.view.View.VISIBLE
            } else {
                tvError.visibility = android.view.View.GONE
            }

            // Update log output with current state info
            val logText = buildString {
                appendLine("=== Emoji Feature Test Status ===")
                appendLine("Loading: ${state.isLoading}")
                appendLine("Refreshing: ${state.isRefreshing}")
                appendLine("Has Cached Data: ${state.hasCachedData}")
                appendLine("Total Styles: ${state.allStyles.size}")
                appendLine("Displayed Styles: ${state.displayedStyles.size}")
                appendLine("Selected Style: ${state.selectedStyleId}")
                appendLine("Search Query: '${state.searchQuery}'")
                appendLine("Selected Category: ${state.selectedCategory?.displayName ?: "All"}")
                appendLine("Show Only Free: ${state.showOnlyFree}")
                appendLine("Show Only Premium: ${state.showOnlyPremium}")
                appendLine("Show Only Popular: ${state.showOnlyPopular}")
                appendLine("Last Refresh: ${if (state.lastRefreshTimestamp > 0) java.util.Date(state.lastRefreshTimestamp) else "Never"}")
                if (state.errorMessage != null) {
                    appendLine("Error: ${state.errorMessage}")
                }
                if (state.navigationEvent != null) {
                    appendLine("Navigation Event: ${state.navigationEvent}")
                }
                appendLine("=== End Status ===")
            }
            tvLogOutput.text = logText
        }
    }
    
    private fun testEmojiLoad() {
        BatteryLogger.d("EmojiTestActivity", "testEmojiLoad: Testing emoji data loading")
        emojiViewModel.handleEvent(BatteryGalleryEvent.LoadInitialData)
    }

    private fun testEmojiSelection() {
        BatteryLogger.d("EmojiTestActivity", "testEmojiSelection: Testing emoji filtering and search")
        // Test search functionality
        emojiViewModel.handleEvent(BatteryGalleryEvent.SearchStyles("battery"))

        // Test category filtering
        emojiViewModel.handleEvent(BatteryGalleryEvent.FilterByCategory(
            com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.CHARACTER
        ))
    }

    private fun testEmojiDisplay() {
        BatteryLogger.d("EmojiTestActivity", "testEmojiDisplay: Testing emoji refresh and data display")
        // Test data refresh
        emojiViewModel.handleEvent(BatteryGalleryEvent.RefreshData)

        // Test clearing filters
        emojiViewModel.handleEvent(BatteryGalleryEvent.ClearAllFilters)
    }
}
