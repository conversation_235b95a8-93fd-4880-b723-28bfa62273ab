package com.tqhit.battery.one.features.emoji.domain.model
import com.tqhit.battery.one.utils.BatteryLogger

/**
 * Complete user customization configuration for the emoji battery feature.
 * Extends BatteryStyleConfig with additional user-specific settings and overlay preferences.
 * 
 * This model represents the complete state of user customizations including:
 * - Selected battery style and emoji
 * - Visual customization settings (size, color, visibility)
 * - Overlay behavior and positioning
 * - Feature enablement state
 * 
 * @param selectedStyleId ID of the currently selected battery style
 * @param selectedBatteryImageUrl URL of the selected battery container image
 * @param selectedEmojiImageUrl URL of the selected emoji/character image
 * @param styleConfig Visual configuration settings for the style
 * @param isFeatureEnabled Whether the emoji battery overlay is enabled
 * @param overlayPosition Position of the overlay on screen (future enhancement)
 * @param lastModifiedTimestamp When this configuration was last modified
 */
data class CustomizationConfig(
    val selectedStyleId: String = "",
    val selectedBatteryImageUrl: String = "",
    val selectedEmojiImageUrl: String = "",
    val styleConfig: BatteryStyleConfig = BatteryStyleConfig(),
    val isFeatureEnabled: Boolean = false,
    val overlayPosition: OverlayPosition = OverlayPosition.TOP_CENTER,
    val lastModifiedTimestamp: Long = System.currentTimeMillis()
) {
    
    /**
     * Validates that the customization configuration is complete and valid.
     * 
     * @return true if all required fields are present and configuration is valid
     */
    fun isValid(): Boolean {
        return selectedStyleId.isNotBlank() &&
               selectedBatteryImageUrl.isNotBlank() &&
               selectedEmojiImageUrl.isNotBlank() &&
               styleConfig.isValid() &&
               lastModifiedTimestamp > 0
    }
    
    /**
     * Checks if the configuration has been customized from defaults.
     * 
     * @return true if user has made customizations beyond default values
     */
    fun isCustomized(): Boolean {
        return selectedStyleId.isNotBlank() ||
               isFeatureEnabled ||
               styleConfig != BatteryStyleConfig()
    }
    
    /**
     * Creates a validated copy of this configuration with clamped values.
     * 
     * @return A validated copy with all values within acceptable ranges
     */
    fun validated(): CustomizationConfig {
        return copy(
            styleConfig = styleConfig.validated(),
            lastModifiedTimestamp = if (lastModifiedTimestamp <= 0) System.currentTimeMillis() else lastModifiedTimestamp
        )
    }
    
    /**
     * Creates a copy with updated style selection.
     * 
     * @param batteryStyle The new battery style to apply
     * @return Updated configuration with new style
     */
    fun withStyle(batteryStyle: BatteryStyle): CustomizationConfig {
        return copy(
            selectedStyleId = batteryStyle.id,
            selectedBatteryImageUrl = batteryStyle.batteryImageUrl,
            selectedEmojiImageUrl = batteryStyle.emojiImageUrl,
            styleConfig = batteryStyle.defaultConfig,
            lastModifiedTimestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * Creates a copy with updated style configuration.
     * 
     * @param newStyleConfig The new style configuration
     * @return Updated configuration with new style settings
     */
    fun withStyleConfig(newStyleConfig: BatteryStyleConfig): CustomizationConfig {
        return copy(
            styleConfig = newStyleConfig.validated(),
            lastModifiedTimestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * Creates a copy with feature enablement toggled.
     * 
     * @param enabled Whether the feature should be enabled
     * @return Updated configuration with new enablement state
     */
    fun withFeatureEnabled(enabled: Boolean): CustomizationConfig {
        return copy(
            isFeatureEnabled = enabled,
            lastModifiedTimestamp = System.currentTimeMillis()
        )
    }
    
    companion object {
        private const val TAG = "CustomizationConfig"
        
        /**
         * Creates a default customization configuration.
         * Used when no user customizations exist.
         * 
         * @return Default CustomizationConfig with safe fallback values
         */
        fun createDefault(): CustomizationConfig {
            return CustomizationConfig()
        }
        
        /**
         * Creates a customization configuration from a battery style.
         * Used when user selects a style for the first time.
         * 
         * @param batteryStyle The battery style to base configuration on
         * @return CustomizationConfig initialized with the style
         */
        fun fromBatteryStyle(batteryStyle: BatteryStyle): CustomizationConfig {
            return CustomizationConfig(
                selectedStyleId = batteryStyle.id,
                selectedBatteryImageUrl = batteryStyle.batteryImageUrl,
                selectedEmojiImageUrl = batteryStyle.emojiImageUrl,
                styleConfig = batteryStyle.defaultConfig,
                isFeatureEnabled = false, // User must explicitly enable
                lastModifiedTimestamp = System.currentTimeMillis()
            )
        }
    }
}

/**
 * Represents the position of the emoji battery overlay on the screen.
 * Future enhancement for allowing users to position the overlay.
 */
enum class OverlayPosition(val displayName: String) {
    TOP_LEFT("Top Left"),
    TOP_CENTER("Top Center"),
    TOP_RIGHT("Top Right"),
    CENTER_LEFT("Center Left"),
    CENTER("Center"),
    CENTER_RIGHT("Center Right"),
    BOTTOM_LEFT("Bottom Left"),
    BOTTOM_CENTER("Bottom Center"),
    BOTTOM_RIGHT("Bottom Right");
    
    companion object {
        /**
         * Gets the default overlay position.
         * 
         * @return Default OverlayPosition
         */
        fun getDefault(): OverlayPosition = TOP_CENTER
    }
}
