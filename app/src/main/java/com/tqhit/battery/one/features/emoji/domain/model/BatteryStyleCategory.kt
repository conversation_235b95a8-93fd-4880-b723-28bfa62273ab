package com.tqhit.battery.one.features.emoji.domain.model
import com.tqhit.battery.one.utils.BatteryLogger

/**
 * Enumeration of battery style categories for the emoji battery feature.
 * Defines different categories to organize and filter battery styles.
 * 
 * Each category has a display name and emoji icon for UI presentation.
 */
enum class BatteryStyleCategory(
    val displayName: String,
    val emoji: String,
    val sortOrder: Int
) {
    /**
     * Hot/trending styles - featured and popular content
     */
    HOT("HOT", "🔥", 0),
    
    /**
     * Character-based styles - anime, cartoon, mascot characters
     */
    CHARACTER("Character", "👾", 1),
    
    /**
     * Heart-themed styles - romantic, love-themed designs
     */
    HEART("Heart", "❤️", 2),
    
    /**
     * Cute styles - kawaii, adorable designs
     */
    CUTE("Cute", "🥰", 3),
    
    /**
     * Animal-themed styles - pets, wildlife, creatures
     */
    ANIMAL("Animal", "🐱", 4),
    
    /**
     * Food-themed styles - fruits, snacks, beverages
     */
    FOOD("Food", "🍎", 5),
    
    /**
     * Nature-themed styles - plants, weather, landscapes
     */
    NATURE("Nature", "🌸", 6),
    
    /**
     * Gaming-themed styles - game characters, controllers, symbols
     */
    GAMING("Gaming", "🎮", 7),
    
    /**
     * Seasonal styles - holiday, seasonal themes
     */
    SEASONAL("Seasonal", "🎄", 8),
    
    /**
     * Minimalist styles - simple, clean designs
     */
    MINIMAL("Minimal", "⚪", 9);
    
    /**
     * Gets the category display text with emoji.
     * 
     * @return Formatted display text (e.g., "🔥 HOT")
     */
    fun getDisplayText(): String {
        return "$emoji $displayName"
    }
    
    /**
     * Checks if this category should be featured prominently.
     * HOT category is always featured.
     * 
     * @return true if this category should be featured
     */
    fun isFeatured(): Boolean {
        return this == HOT
    }
    
    companion object {
        /**
         * Gets all categories sorted by their display order.
         * 
         * @return List of categories in display order
         */
        fun getAllSorted(): List<BatteryStyleCategory> {
            return values().sortedBy { it.sortOrder }
        }
        
        /**
         * Gets categories that should be shown in the main filter tabs.
         * Excludes less common categories to avoid UI clutter.
         * 
         * @return List of main filter categories
         */
        fun getMainFilterCategories(): List<BatteryStyleCategory> {
            return listOf(HOT, CHARACTER, HEART, CUTE, ANIMAL, FOOD)
        }
        
        /**
         * Finds a category by its display name (case-insensitive).
         * 
         * @param displayName The display name to search for
         * @return The matching category, or null if not found
         */
        fun findByDisplayName(displayName: String): BatteryStyleCategory? {
            return values().find { 
                it.displayName.equals(displayName, ignoreCase = true) 
            }
        }
        
        /**
         * Gets the default category for new styles.
         * 
         * @return Default category (CHARACTER)
         */
        fun getDefault(): BatteryStyleCategory {
            return CHARACTER
        }
        
        /**
         * Creates a category from a string value, with fallback to default.
         * Used for parsing data from remote config or local storage.
         * 
         * @param value String representation of the category
         * @return The matching category, or default if not found
         */
        fun fromString(value: String?): BatteryStyleCategory {
            if (value.isNullOrBlank()) return getDefault()
            
            return try {
                valueOf(value.uppercase())
            } catch (e: IllegalArgumentException) {
                findByDisplayName(value) ?: getDefault()
            }
        }
    }
}
