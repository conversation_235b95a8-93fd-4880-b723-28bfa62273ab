package com.tqhit.battery.one.features.emoji.presentation.customize.handler

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Handles configuration editing events for the emoji customization feature.
 * 
 * Responsibilities:
 * - Emoji visibility toggles
 * - Percentage visibility toggles
 * - Font size changes
 * - Emoji size changes
 * - Color changes
 * - Overlay position changes
 * 
 * Follows the stats module architecture pattern with comprehensive logging.
 */
class ConfigurationHandler @Inject constructor(
    private val loadCustomizationUseCase: LoadCustomizationUseCase,
    private val saveCustomizationUseCase: SaveCustomizationUseCase
) {
    
    companion object {
        private const val TAG = "EmojiConfigHandler"
    }
    
    /**
     * Toggles emoji visibility in the configuration.
     */
    fun handleToggleEmojiVisibility(
        showEmoji: Boolean,
        uiState: MutableStateFlow<CustomizeState>,
        onConfigUpdate: (BatteryStyleConfig) -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_CONFIG_EDIT: Toggle emoji visibility: $showEmoji")
        onConfigUpdate(uiState.value.editingConfig.copy(showEmoji = showEmoji))
    }
    
    /**
     * Toggles percentage visibility in the configuration.
     */
    fun handleTogglePercentageVisibility(
        showPercentage: Boolean,
        uiState: MutableStateFlow<CustomizeState>,
        onConfigUpdate: (BatteryStyleConfig) -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_CONFIG_EDIT: Toggle percentage visibility: $showPercentage")
        onConfigUpdate(uiState.value.editingConfig.copy(showPercentage = showPercentage))
    }
    
    /**
     * Changes the percentage font size with validation.
     */
    fun handleChangePercentageFontSize(
        sizeDp: Int,
        uiState: MutableStateFlow<CustomizeState>,
        onConfigUpdate: (BatteryStyleConfig) -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_CONFIG_EDIT: Change percentage font size: ${sizeDp}dp")
        val clampedSize = sizeDp.coerceIn(5, 40)
        onConfigUpdate(uiState.value.editingConfig.copy(percentageFontSizeDp = clampedSize))
    }
    
    /**
     * Changes the emoji size scale with validation.
     */
    fun handleChangeEmojiSize(
        scale: Float,
        uiState: MutableStateFlow<CustomizeState>,
        onConfigUpdate: (BatteryStyleConfig) -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_CONFIG_EDIT: Change emoji size: ${scale}x")
        val clampedScale = scale.coerceIn(0.5f, 2.0f)
        onConfigUpdate(uiState.value.editingConfig.copy(emojiSizeScale = clampedScale))
    }
    
    /**
     * Changes the percentage text color.
     */
    fun handleChangePercentageColor(
        color: Int,
        uiState: MutableStateFlow<CustomizeState>,
        onConfigUpdate: (BatteryStyleConfig) -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_CONFIG_EDIT: Change percentage color: $color")
        onConfigUpdate(uiState.value.editingConfig.copy(percentageColor = color))
    }
    
    /**
     * Changes the overlay position in the main configuration.
     */
    fun handleChangeOverlayPosition(
        position: OverlayPosition,
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope
    ) {
        BatteryLogger.d(TAG, "EMOJI_CONFIG_EDIT: Change overlay position: ${position.displayName}")
        
        // This updates the main configuration, not just style config
        coroutineScope.launch {
            try {
                val currentConfig = loadCustomizationUseCase.getCurrentCustomizationConfig()
                val updatedConfig = currentConfig.copy(overlayPosition = position)
                saveCustomizationUseCase.saveCustomizationConfig(updatedConfig)
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "EMOJI_CONFIG_EDIT: Failed to update overlay position", e)
                uiState.value = uiState.value.withError("Failed to update position: ${e.message}")
            }
        }
    }
}
