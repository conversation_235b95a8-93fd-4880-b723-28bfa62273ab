package com.tqhit.battery.one.features.emoji.presentation.customize.handler

import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Handles style selection events for the emoji customization feature.
 * 
 * Responsibilities:
 * - Battery style selection and saving
 * - Battery container selection
 * - Emoji character selection
 * - Style browsing navigation
 * 
 * Follows the stats module architecture pattern with comprehensive logging.
 */
class StyleSelectionHandler @Inject constructor(
    private val saveCustomizationUseCase: SaveCustomizationUseCase
) {
    
    companion object {
        private const val TAG = "EmojiStyleHandler"
    }
    
    /**
     * Handles battery style selection and saves it to the configuration.
     * 
     * @param style The selected battery style
     * @param uiState Current UI state flow
     * @param coroutineScope Coroutine scope for async operations
     * @param onPreviewUpdate Callback to schedule preview update
     */
    fun handleSelectBatteryStyle(
        style: BatteryStyle,
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope,
        onPreviewUpdate: () -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_STYLE_SELECTION: Selected battery style: ${style.name}")
        
        coroutineScope.launch {
            try {
                val result = saveCustomizationUseCase.saveCustomizationFromStyle(style, enableFeature = false)
                if (result.isSuccess) {
                    BatteryLogger.d(TAG, "EMOJI_STYLE_SELECTION: Successfully saved style selection")
                    onPreviewUpdate()
                } else {
                    val exception = result.exceptionOrNull()
                    if (exception != null) {
                        BatteryLogger.e(TAG, "EMOJI_STYLE_SELECTION: Failed to save style selection", exception)
                    } else {
                        BatteryLogger.w(TAG, "EMOJI_STYLE_SELECTION: Failed to save style selection - no exception details")
                    }
                    uiState.value = uiState.value.withError("Failed to select style: ${exception?.message}")
                }
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "EMOJI_STYLE_SELECTION: Exception selecting style", e)
                uiState.value = uiState.value.withError("Error selecting style: ${e.message}")
            }
        }
    }
    
    /**
     * Handles battery container selection from a style.
     * 
     * @param style The style containing the desired battery container
     * @param uiState Current UI state flow
     * @param onAutoSave Callback to schedule auto-save
     * @param onPreviewUpdate Callback to schedule preview update
     */
    fun handleSelectBatteryContainer(
        style: BatteryStyle,
        uiState: MutableStateFlow<CustomizeState>,
        onAutoSave: () -> Unit,
        onPreviewUpdate: () -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_STYLE_SELECTION: Selected battery container from style: ${style.name}")
        
        val currentState = uiState.value
        val updatedState = currentState.withStyleSelection(batteryStyleId = style.id)
        uiState.value = updatedState
        
        onAutoSave()
        onPreviewUpdate()
    }
    
    /**
     * Handles emoji character selection from a style.
     * 
     * @param style The style containing the desired emoji character
     * @param uiState Current UI state flow
     * @param onAutoSave Callback to schedule auto-save
     * @param onPreviewUpdate Callback to schedule preview update
     */
    fun handleSelectEmojiCharacter(
        style: BatteryStyle,
        uiState: MutableStateFlow<CustomizeState>,
        onAutoSave: () -> Unit,
        onPreviewUpdate: () -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_STYLE_SELECTION: Selected emoji character from style: ${style.name}")
        
        val currentState = uiState.value
        val updatedState = currentState.withStyleSelection(emojiStyleId = style.id)
        uiState.value = updatedState
        
        onAutoSave()
        onPreviewUpdate()
    }
    
    /**
     * Handles request to browse more styles.
     * This is typically handled by the fragment for navigation.
     */
    fun handleBrowseMoreStyles() {
        BatteryLogger.d(TAG, "EMOJI_NAVIGATION: Browse more styles requested")
        // Navigation will be handled by the fragment
    }
}
