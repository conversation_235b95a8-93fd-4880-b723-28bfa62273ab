package com.tqhit.battery.one.features.emoji.presentation.customize.manager

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Manages preview updates and auto-save functionality for emoji customization.
 * 
 * Responsibilities:
 * - Schedule debounced preview updates
 * - Schedule debounced auto-save operations
 * - Manage preview state and timing
 * - Handle battery level changes for live preview
 * 
 * Follows the stats module architecture pattern with comprehensive logging.
 */
class PreviewManager @Inject constructor() {
    
    companion object {
        private const val TAG = "EmojiPreviewManager"
        private const val PREVIEW_UPDATE_DELAY_MS = 300L
        private const val AUTO_SAVE_DELAY_MS = 2000L
    }
    
    // Jobs for managing coroutines
    private var previewUpdateJob: Job? = null
    private var autoSaveJob: Job? = null
    
    /**
     * Schedules a preview update with debouncing.
     */
    fun schedulePreviewUpdate(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope
    ) {
        previewUpdateJob?.cancel()
        previewUpdateJob = coroutineScope.launch {
            delay(PREVIEW_UPDATE_DELAY_MS)
            uiState.value = uiState.value.copy(previewUpdateTimestamp = System.currentTimeMillis())
            BatteryLogger.d(TAG, "EMOJI_PREVIEW: Preview updated")
        }
    }
    
    /**
     * Schedules auto-save with debouncing.
     */
    fun scheduleAutoSave(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope,
        onSaveConfiguration: suspend () -> Result<Unit>
    ) {
        autoSaveJob?.cancel()
        autoSaveJob = coroutineScope.launch {
            delay(AUTO_SAVE_DELAY_MS)
            val currentState = uiState.value
            if (currentState.canSave()) {
                BatteryLogger.d(TAG, "EMOJI_AUTO_SAVE: Performing auto-save")
                onSaveConfiguration()
            }
        }
    }
    
    /**
     * Updates the current style configuration and triggers preview update.
     */
    fun updateStyleConfig(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope,
        update: (BatteryStyleConfig) -> BatteryStyleConfig,
        onAutoSave: () -> Unit
    ) {
        val currentState = uiState.value
        val updatedConfig = update(currentState.editingConfig)
        val validatedConfig = updatedConfig.validated()
        
        uiState.value = currentState.withEditingConfig(validatedConfig)
        
        onAutoSave()
        schedulePreviewUpdate(uiState, coroutineScope)
    }
    
    /**
     * Handles battery level changes for live preview.
     */
    fun handleBatteryLevelChanged(
        level: Int,
        isCharging: Boolean,
        uiState: MutableStateFlow<CustomizeState>
    ) {
        val currentState = uiState.value
        if (currentState.showLivePreview && currentState.previewBatteryLevel != level) {
            BatteryLogger.d(TAG, "EMOJI_BATTERY_UPDATE: Live preview battery level changed: $level%, charging: $isCharging")
            uiState.value = currentState.withUIState(previewBatteryLevel = level)
        }
    }
    
    /**
     * Handles live preview toggle.
     */
    fun handleToggleLivePreview(
        showPreview: Boolean,
        uiState: MutableStateFlow<CustomizeState>
    ) {
        BatteryLogger.d(TAG, "EMOJI_UI: Toggle live preview: $showPreview")
        uiState.value = uiState.value.withUIState(showLivePreview = showPreview)
    }
    
    /**
     * Handles manual preview battery level change.
     */
    fun handleChangePreviewBatteryLevel(
        level: Int,
        uiState: MutableStateFlow<CustomizeState>
    ) {
        BatteryLogger.d(TAG, "EMOJI_UI: Change preview battery level: $level%")
        val clampedLevel = level.coerceIn(0, 100)
        uiState.value = uiState.value.withUIState(previewBatteryLevel = clampedLevel)
    }
    
    /**
     * Handles manual preview refresh.
     */
    fun handleRefreshPreview(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope
    ) {
        BatteryLogger.d(TAG, "EMOJI_UI: Refresh preview")
        schedulePreviewUpdate(uiState, coroutineScope)
    }
    
    /**
     * Cancels all pending jobs.
     */
    fun cancelJobs() {
        BatteryLogger.d(TAG, "EMOJI_LIFECYCLE: Cancelling preview manager jobs")
        previewUpdateJob?.cancel()
        autoSaveJob?.cancel()
    }
}
