package com.tqhit.battery.one.features.emoji.presentation.customize
import com.tqhit.battery.one.utils.BatteryLogger

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition

/**
 * Events for the customization screen.
 * 
 * Represents all possible user interactions and system events that can occur
 * in the customization interface. Follows the MVI pattern established in Phase 2
 * with comprehensive event coverage for complex UI interactions.
 * 
 * Event categories:
 * - Lifecycle events (screen enter/exit, resume/pause)
 * - Style selection events (battery, emoji, combined styles)
 * - Configuration editing events (sliders, toggles, color picker)
 * - Save/apply events (save configuration, apply changes, reset)
 * - UI interaction events (dialogs, preview, navigation)
 * - Error handling events (retry, dismiss errors)
 */
sealed class CustomizeEvent {
    
    // Lifecycle Events
    /**
     * Screen is being initialized or resumed.
     */
    object OnScreenEnter : CustomizeEvent()
    
    /**
     * Screen is being paused or exited.
     */
    object OnScreenExit : CustomizeEvent()
    
    /**
     * Screen resumed from background.
     */
    object OnResume : CustomizeEvent()
    
    /**
     * Screen paused to background.
     */
    object OnPause : CustomizeEvent()
    
    // Style Selection Events
    /**
     * User selected a complete battery style (battery + emoji combination).
     * 
     * @param style The BatteryStyle that was selected
     */
    data class SelectBatteryStyle(val style: BatteryStyle) : CustomizeEvent()
    
    /**
     * User selected a battery container style only.
     * 
     * @param style The BatteryStyle containing the battery image
     */
    data class SelectBatteryContainer(val style: BatteryStyle) : CustomizeEvent()
    
    /**
     * User selected an emoji/character style only.
     * 
     * @param style The BatteryStyle containing the emoji image
     */
    data class SelectEmojiCharacter(val style: BatteryStyle) : CustomizeEvent()
    
    /**
     * User wants to browse more styles (navigate back to gallery).
     */
    object BrowseMoreStyles : CustomizeEvent()
    
    // Configuration Editing Events
    /**
     * User toggled emoji visibility.
     * 
     * @param showEmoji Whether emoji should be visible
     */
    data class ToggleEmojiVisibility(val showEmoji: Boolean) : CustomizeEvent()
    
    /**
     * User toggled percentage text visibility.
     * 
     * @param showPercentage Whether percentage text should be visible
     */
    data class TogglePercentageVisibility(val showPercentage: Boolean) : CustomizeEvent()
    
    /**
     * User changed percentage font size.
     * 
     * @param sizeDp Font size in dp (5-40)
     */
    data class ChangePercentageFontSize(val sizeDp: Int) : CustomizeEvent()
    
    /**
     * User changed emoji size scale.
     * 
     * @param scale Scale factor (0.5-2.0)
     */
    data class ChangeEmojiSize(val scale: Float) : CustomizeEvent()
    
    /**
     * User selected a new percentage text color.
     * 
     * @param color Color in ARGB format
     */
    data class ChangePercentageColor(val color: Int) : CustomizeEvent()
    
    /**
     * User changed overlay position.
     * 
     * @param position New overlay position
     */
    data class ChangeOverlayPosition(val position: OverlayPosition) : CustomizeEvent()
    
    // Save and Apply Events
    /**
     * User wants to save current configuration.
     */
    object SaveConfiguration : CustomizeEvent()
    
    /**
     * User wants to apply configuration and enable feature.
     */
    object ApplyAndEnable : CustomizeEvent()
    
    /**
     * User wants to reset configuration to defaults.
     */
    object ResetToDefaults : CustomizeEvent()
    
    /**
     * User confirmed reset action.
     */
    object ConfirmReset : CustomizeEvent()
    
    /**
     * User wants to discard unsaved changes.
     */
    object DiscardChanges : CustomizeEvent()
    
    // UI Interaction Events
    /**
     * User wants to open color picker.
     */
    object OpenColorPicker : CustomizeEvent()
    
    /**
     * User wants to close color picker.
     */
    object CloseColorPicker : CustomizeEvent()
    
    /**
     * User wants to open position selector.
     */
    object OpenPositionSelector : CustomizeEvent()
    
    /**
     * User wants to close position selector.
     */
    object ClosePositionSelector : CustomizeEvent()
    
    /**
     * User toggled live preview visibility.
     * 
     * @param showPreview Whether to show live preview
     */
    data class ToggleLivePreview(val showPreview: Boolean) : CustomizeEvent()
    
    /**
     * User changed preview battery level for testing.
     * 
     * @param level Battery level percentage (0-100)
     */
    data class ChangePreviewBatteryLevel(val level: Int) : CustomizeEvent()
    
    /**
     * User wants to refresh preview with current battery level.
     */
    object RefreshPreview : CustomizeEvent()
    
    // Permission and Feature Events
    /**
     * User wants to request required permissions.
     */
    object RequestPermissions : CustomizeEvent()
    
    /**
     * User wants to enable accessibility service.
     */
    object EnableAccessibilityService : CustomizeEvent()
    
    /**
     * User toggled feature enablement.
     * 
     * @param enabled Whether feature should be enabled
     */
    data class ToggleFeatureEnabled(val enabled: Boolean) : CustomizeEvent()
    
    // Error Handling Events
    /**
     * User wants to retry failed operation.
     */
    object RetryOperation : CustomizeEvent()
    
    /**
     * User dismissed error message.
     */
    object DismissError : CustomizeEvent()
    
    /**
     * User wants to validate current configuration.
     */
    object ValidateConfiguration : CustomizeEvent()
    
    // Navigation Events
    /**
     * User wants to go back to previous screen.
     */
    object NavigateBack : CustomizeEvent()
    
    /**
     * User wants to navigate to settings.
     */
    object NavigateToSettings : CustomizeEvent()
    
    /**
     * User wants to navigate to help/tutorial.
     */
    object NavigateToHelp : CustomizeEvent()
    
    // Data Events (Internal)
    /**
     * Customization data was loaded.
     * 
     * @param success Whether loading was successful
     */
    data class CustomizationDataLoaded(val success: Boolean) : CustomizeEvent()
    
    /**
     * Styles data was loaded.
     * 
     * @param success Whether loading was successful
     */
    data class StylesDataLoaded(val success: Boolean) : CustomizeEvent()
    
    /**
     * Configuration was saved successfully.
     */
    object ConfigurationSaved : CustomizeEvent()
    
    /**
     * Configuration save failed.
     * 
     * @param error Error message
     */
    data class ConfigurationSaveFailed(val error: String) : CustomizeEvent()
    
    /**
     * Battery level changed (from CoreBatteryStatsProvider).
     * 
     * @param level New battery level
     * @param isCharging Whether device is charging
     */
    data class BatteryLevelChanged(val level: Int, val isCharging: Boolean) : CustomizeEvent()
    
    // Premium and Monetization Events (for Phase 5)
    /**
     * User wants to unlock premium style.
     * 
     * @param style The premium style to unlock
     */
    data class UnlockPremiumStyle(val style: BatteryStyle) : CustomizeEvent()
    
    /**
     * User wants to watch ad to unlock premium content.
     * 
     * @param style The style to unlock via ad
     */
    data class WatchAdToUnlock(val style: BatteryStyle) : CustomizeEvent()
    
    /**
     * User wants to purchase premium access.
     */
    object PurchasePremiumAccess : CustomizeEvent()
    
    // Backup and Export Events
    /**
     * User wants to export current configuration.
     */
    object ExportConfiguration : CustomizeEvent()
    
    /**
     * User wants to import configuration.
     * 
     * @param data Configuration data to import
     */
    data class ImportConfiguration(val data: String) : CustomizeEvent()
    
    /**
     * User wants to share current configuration.
     */
    object ShareConfiguration : CustomizeEvent()
}

/**
 * Helper functions for event categorization and handling.
 */
object CustomizeEventUtils {
    
    /**
     * Checks if the event requires saving state.
     */
    fun requiresSaving(event: CustomizeEvent): Boolean {
        return when (event) {
            is CustomizeEvent.ToggleEmojiVisibility,
            is CustomizeEvent.TogglePercentageVisibility,
            is CustomizeEvent.ChangePercentageFontSize,
            is CustomizeEvent.ChangeEmojiSize,
            is CustomizeEvent.ChangePercentageColor,
            is CustomizeEvent.ChangeOverlayPosition,
            is CustomizeEvent.SelectBatteryStyle,
            is CustomizeEvent.SelectBatteryContainer,
            is CustomizeEvent.SelectEmojiCharacter -> true
            else -> false
        }
    }
    
    /**
     * Checks if the event is a UI interaction that doesn't modify data.
     */
    fun isUIInteraction(event: CustomizeEvent): Boolean {
        return when (event) {
            is CustomizeEvent.OpenColorPicker,
            is CustomizeEvent.CloseColorPicker,
            is CustomizeEvent.OpenPositionSelector,
            is CustomizeEvent.ClosePositionSelector,
            is CustomizeEvent.ToggleLivePreview,
            is CustomizeEvent.ChangePreviewBatteryLevel,
            is CustomizeEvent.RefreshPreview -> true
            else -> false
        }
    }
    
    /**
     * Checks if the event is a navigation event.
     */
    fun isNavigation(event: CustomizeEvent): Boolean {
        return when (event) {
            is CustomizeEvent.NavigateBack,
            is CustomizeEvent.NavigateToSettings,
            is CustomizeEvent.NavigateToHelp,
            is CustomizeEvent.BrowseMoreStyles -> true
            else -> false
        }
    }
}
