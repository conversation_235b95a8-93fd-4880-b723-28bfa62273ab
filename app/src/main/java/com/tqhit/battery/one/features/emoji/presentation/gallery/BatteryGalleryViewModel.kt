package com.tqhit.battery.one.features.emoji.presentation.gallery

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Battery Gallery screen following MVI pattern.
 * 
 * This ViewModel follows the established stats module architecture pattern:
 * - Integrates with CoreBatteryStatsProvider for battery state monitoring
 * - Uses StateFlow for reactive UI state management
 * - Implements comprehensive error handling and logging
 * - Supports filtering, searching, and categorization
 * - Handles premium content and monetization flows
 * - Provides debounced search functionality
 * - Manages feature toggle state and permissions
 */
@OptIn(FlowPreview::class)
@HiltViewModel
class BatteryGalleryViewModel @Inject constructor(
    private val getBatteryStylesUseCase: GetBatteryStylesUseCase,
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) : ViewModel() {
    
    companion object {
        private const val TAG = "EmojiGallery"
        private const val SEARCH_DEBOUNCE_MS = 300L
    }
    
    // Private mutable state
    private val _uiState = MutableStateFlow(BatteryGalleryState.initial())
    
    // Public read-only state
    val uiState: StateFlow<BatteryGalleryState> = _uiState.asStateFlow()
    
    // Search query flow for debouncing
    private val _searchQuery = MutableStateFlow("")
    
    init {
        BatteryLogger.d(TAG, "EMOJI_INIT: Initializing BatteryGalleryViewModel")
        initializeViewModel()
    }

    /**
     * Initializes the ViewModel by setting up data flows and loading initial data.
     */
    private fun initializeViewModel() {
        BatteryLogger.d(TAG, "EMOJI_INIT: Setting up data flows and observers")

        // Collect battery styles from use case
        viewModelScope.launch {
            getBatteryStylesUseCase.getAllStylesFlow().collect { styles ->
                BatteryLogger.d(TAG, "EMOJI_STYLES_UPDATE: Received ${styles.size} styles from use case")
                updateStylesInState(styles)
            }
        }

        // Collect loading state from use case
        viewModelScope.launch {
            getBatteryStylesUseCase.getLoadingStateFlow().collect { isLoading ->
                BatteryLogger.d(TAG, "EMOJI_LOADING_UPDATE: Loading state changed to $isLoading")
                _uiState.value = _uiState.value.copy(isLoading = isLoading)
            }
        }

        // Collect battery status for feature integration
        viewModelScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .filterNotNull()
                .distinctUntilChanged { old, new ->
                    old.percentage == new.percentage && old.isCharging == new.isCharging
                }
                .collect { batteryStatus ->
                    BatteryLogger.d(TAG, "EMOJI_BATTERY_UPDATE: Battery ${batteryStatus.percentage}%, charging: ${batteryStatus.isCharging}")
                    handleBatteryStateChanged(batteryStatus.isCharging, batteryStatus.percentage)
                }
        }

        // Set up debounced search
        viewModelScope.launch {
            _searchQuery
                .debounce(SEARCH_DEBOUNCE_MS)
                .distinctUntilChanged()
                .collect { query ->
                    BatteryLogger.d(TAG, "EMOJI_SEARCH_DEBOUNCED: Processing search query: '$query'")
                    performSearch(query)
                }
        }

        // Load initial data
        handleEvent(BatteryGalleryEvent.LoadInitialData)
    }
    
    /**
     * Handles events from the UI following MVI pattern.
     */
    fun handleEvent(event: BatteryGalleryEvent) {
        BatteryLogger.d(TAG, "EMOJI_EVENT: Handling ${event::class.simpleName}")

        when (event) {
            is BatteryGalleryEvent.LoadInitialData -> loadInitialData()
            is BatteryGalleryEvent.RefreshData -> refreshData()
            is BatteryGalleryEvent.RetryLoading -> retryLoading()

            is BatteryGalleryEvent.FilterByCategory -> filterByCategory(event.category)
            is BatteryGalleryEvent.ToggleShowOnlyFree -> toggleShowOnlyFree(event.showOnlyFree)
            is BatteryGalleryEvent.ToggleShowOnlyPremium -> toggleShowOnlyPremium(event.showOnlyPremium)
            is BatteryGalleryEvent.ToggleShowOnlyPopular -> toggleShowOnlyPopular(event.showOnlyPopular)
            is BatteryGalleryEvent.SearchStyles -> handleSearchInput(event.query)
            is BatteryGalleryEvent.ClearAllFilters -> clearAllFilters()

            is BatteryGalleryEvent.SelectStyle -> selectStyle(event.style)
            is BatteryGalleryEvent.CustomizeStyle -> customizeStyle(event.style)
            is BatteryGalleryEvent.ToggleCategoryFilter -> toggleCategoryFilter(event.show)
            is BatteryGalleryEvent.ToggleSearchBar -> toggleSearchBar(event.show)
            is BatteryGalleryEvent.PreviewStyle -> previewStyle(event.style)

            is BatteryGalleryEvent.ToggleEmojiBatteryFeature -> toggleEmojiBatteryFeature(event.enabled)
            is BatteryGalleryEvent.RequestPermissions -> requestPermissions()
            is BatteryGalleryEvent.PermissionsGranted -> handlePermissionsGranted()
            is BatteryGalleryEvent.PermissionsDenied -> handlePermissionsDenied()

            is BatteryGalleryEvent.UnlockPremiumStyle -> unlockPremiumStyle(event.style)
            is BatteryGalleryEvent.WatchAdToUnlock -> watchAdToUnlock(event.style)
            is BatteryGalleryEvent.PurchasePremiumAccess -> purchasePremiumAccess()
            is BatteryGalleryEvent.PremiumUnlocked -> handlePremiumUnlocked(event.styleId)

            is BatteryGalleryEvent.DismissError -> dismissError()
            is BatteryGalleryEvent.ImageLoadFailed -> handleImageLoadFailed(event.styleId, event.imageType)

            is BatteryGalleryEvent.NavigateToCustomization -> navigateToCustomization(event.style)
            is BatteryGalleryEvent.NavigateToSettings -> navigateToSettings()
            is BatteryGalleryEvent.ShowFeatureInfo -> showFeatureInfo()

            is BatteryGalleryEvent.OnResume -> handleOnResume()
            is BatteryGalleryEvent.OnPause -> handleOnPause()
            is BatteryGalleryEvent.BatteryStateChanged -> handleBatteryStateChanged(event.isCharging, event.batteryLevel)
        }
    }
    
    /**
     * Loads initial data from the repository.
     */
    private fun loadInitialData() {
        BatteryLogger.d(TAG, "EMOJI_LOAD_INITIAL: Loading initial battery styles data")

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    errorMessage = null
                )

                // Check if we have cached data
                val hasCachedData = getBatteryStylesUseCase.hasCachedData()
                BatteryLogger.d(TAG, "EMOJI_LOAD_INITIAL: Has cached data: $hasCachedData")

                if (!hasCachedData) {
                    // Force refresh if no cached data
                    BatteryLogger.d(TAG, "EMOJI_LOAD_INITIAL: No cached data, forcing refresh")
                    val styles = getBatteryStylesUseCase.getAllStyles(forceRefresh = true)
                    updateStylesInState(styles)
                } else {
                    // Use cached data and refresh in background
                    BatteryLogger.d(TAG, "EMOJI_LOAD_INITIAL: Using cached data, refreshing in background")
                    val cachedStyles = getBatteryStylesUseCase.getCurrentStyles()
                    updateStylesInState(cachedStyles)

                    // Background refresh
                    getBatteryStylesUseCase.refreshStyles()
                }

            } catch (e: Exception) {
                BatteryLogger.e(TAG, "EMOJI_LOAD_INITIAL: Failed to load initial data", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to load battery styles: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Refreshes data from remote config.
     */
    private fun refreshData() {
        BatteryLogger.d(TAG, "EMOJI_REFRESH: Refreshing battery styles data")

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isRefreshing = true,
                    errorMessage = null
                )

                val success = getBatteryStylesUseCase.refreshStyles()

                if (!success) {
                    BatteryLogger.w(TAG, "EMOJI_REFRESH: Refresh failed, no data received")
                    _uiState.value = _uiState.value.copy(
                        isRefreshing = false,
                        errorMessage = "Failed to refresh data. Please try again."
                    )
                } else {
                    BatteryLogger.d(TAG, "EMOJI_REFRESH: Refresh completed successfully")
                    _uiState.value = _uiState.value.copy(
                        isRefreshing = false,
                        lastRefreshTimestamp = System.currentTimeMillis()
                    )
                }

            } catch (e: Exception) {
                BatteryLogger.e(TAG, "EMOJI_REFRESH: Failed to refresh data", e)
                _uiState.value = _uiState.value.copy(
                    isRefreshing = false,
                    errorMessage = "Refresh failed: ${e.message}"
                )
            }
        }
    }

    /**
     * Retries loading after an error.
     */
    private fun retryLoading() {
        BatteryLogger.d(TAG, "EMOJI_RETRY: Retrying data load")
        loadInitialData()
    }
    
    /**
     * Updates styles in state and applies current filters.
     */
    private fun updateStylesInState(styles: List<BatteryStyle>) {
        BatteryLogger.d(TAG, "EMOJI_UPDATE_STYLES: Updating state with ${styles.size} styles")

        _uiState.value = _uiState.value.copy(
            allStyles = styles,
            isLoading = false,
            hasCachedData = styles.isNotEmpty(),
            lastRefreshTimestamp = System.currentTimeMillis()
        ).withFilteredStyles()
    }

    /**
     * Filters styles by category.
     */
    private fun filterByCategory(category: BatteryStyleCategory?) {
        BatteryLogger.d(TAG, "EMOJI_FILTER_CATEGORY: Filtering by category: ${category?.displayName ?: "All"}")

        _uiState.value = _uiState.value.copy(
            selectedCategory = category
        ).withFilteredStyles()
    }

    /**
     * Toggles showing only free styles.
     */
    private fun toggleShowOnlyFree(showOnlyFree: Boolean) {
        BatteryLogger.d(TAG, "EMOJI_FILTER_FREE: Toggle show only free: $showOnlyFree")

        _uiState.value = _uiState.value.copy(
            showOnlyFree = showOnlyFree,
            showOnlyPremium = if (showOnlyFree) false else _uiState.value.showOnlyPremium
        ).withFilteredStyles()
    }

    /**
     * Toggles showing only premium styles.
     */
    private fun toggleShowOnlyPremium(showOnlyPremium: Boolean) {
        BatteryLogger.d(TAG, "EMOJI_FILTER_PREMIUM: Toggle show only premium: $showOnlyPremium")

        _uiState.value = _uiState.value.copy(
            showOnlyPremium = showOnlyPremium,
            showOnlyFree = if (showOnlyPremium) false else _uiState.value.showOnlyFree
        ).withFilteredStyles()
    }

    /**
     * Toggles showing only popular styles.
     */
    private fun toggleShowOnlyPopular(showOnlyPopular: Boolean) {
        BatteryLogger.d(TAG, "EMOJI_FILTER_POPULAR: Toggle show only popular: $showOnlyPopular")

        _uiState.value = _uiState.value.copy(
            showOnlyPopular = showOnlyPopular
        ).withFilteredStyles()
    }
    
    /**
     * Handles search input with debouncing.
     */
    private fun handleSearchInput(query: String) {
        BatteryLogger.d(TAG, "EMOJI_SEARCH_INPUT: Search query input: '$query'")
        _searchQuery.value = query
    }

    /**
     * Performs the actual search operation.
     */
    private fun performSearch(query: String) {
        BatteryLogger.d(TAG, "EMOJI_SEARCH_PERFORM: Performing search with query: '$query'")

        _uiState.value = _uiState.value.copy(
            searchQuery = query
        ).withFilteredStyles()
    }

    /**
     * Clears all active filters.
     */
    private fun clearAllFilters() {
        BatteryLogger.d(TAG, "EMOJI_CLEAR_FILTERS: Clearing all filters")

        _uiState.value = _uiState.value.copy(
            selectedCategory = null,
            searchQuery = "",
            showOnlyFree = false,
            showOnlyPremium = false,
            showOnlyPopular = false
        ).withFilteredStyles()

        _searchQuery.value = ""
    }

    /**
     * Handles style selection.
     */
    private fun selectStyle(style: BatteryStyle) {
        BatteryLogger.d(TAG, "EMOJI_SELECT_STYLE: Style selected: ${style.name} (ID: ${style.id})")

        _uiState.value = _uiState.value.copy(
            selectedStyleId = style.id
        )

        // Navigate to customization screen
        navigateToCustomization(style)
    }

    /**
     * Handles style customization request.
     */
    private fun customizeStyle(style: BatteryStyle) {
        BatteryLogger.d(TAG, "EMOJI_CUSTOMIZE_STYLE: Customize style requested: ${style.name} (ID: ${style.id})")

        _uiState.value = _uiState.value.copy(
            selectedStyleId = style.id
        )

        // Navigate to customization screen
        navigateToCustomization(style)
    }
    
    /**
     * Handles battery state changes from CoreBatteryStatsProvider.
     */
    private fun handleBatteryStateChanged(isCharging: Boolean, batteryLevel: Int) {
        BatteryLogger.d(TAG, "EMOJI_BATTERY_STATE: Battery state changed - charging: $isCharging, level: $batteryLevel%")

        // Update state for potential UI changes based on battery state
        // This could be used for dynamic theming or feature availability
        _uiState.value = _uiState.value.copy(
            // Add any battery-state-dependent UI changes here
        )
    }
    
    // Placeholder implementations for other event handlers
    private fun toggleCategoryFilter(show: Boolean) { /* TODO: Implement */ }
    private fun toggleSearchBar(show: Boolean) { /* TODO: Implement */ }
    private fun previewStyle(style: BatteryStyle) { /* TODO: Implement */ }
    private fun toggleEmojiBatteryFeature(enabled: Boolean) { /* TODO: Implement */ }
    private fun requestPermissions() { /* TODO: Implement */ }
    private fun handlePermissionsGranted() { /* TODO: Implement */ }
    private fun handlePermissionsDenied() { /* TODO: Implement */ }
    private fun unlockPremiumStyle(style: BatteryStyle) { /* TODO: Implement */ }
    private fun watchAdToUnlock(style: BatteryStyle) { /* TODO: Implement */ }
    private fun purchasePremiumAccess() { /* TODO: Implement */ }
    private fun handlePremiumUnlocked(styleId: String?) { /* TODO: Implement */ }
    private fun dismissError() { _uiState.value = _uiState.value.copy(errorMessage = null) }
    private fun handleImageLoadFailed(styleId: String, imageType: String) { /* TODO: Implement */ }
    private fun navigateToCustomization(style: BatteryStyle) {
        BatteryLogger.d(TAG, "EMOJI_NAVIGATION: Navigate to customization for style: ${style.name}")
        _uiState.value = _uiState.value.copy(
            navigationEvent = NavigationEvent.NavigateToCustomization(style)
        )
    }

    private fun navigateToSettings() {
        BatteryLogger.d(TAG, "EMOJI_NAVIGATION: Navigate to settings")
        _uiState.value = _uiState.value.copy(
            navigationEvent = NavigationEvent.NavigateToSettings
        )
    }

    private fun showFeatureInfo() {
        BatteryLogger.d(TAG, "EMOJI_NAVIGATION: Show feature info")
        _uiState.value = _uiState.value.copy(
            navigationEvent = NavigationEvent.ShowFeatureInfo
        )
    }

    /**
     * Clears the navigation event after it has been handled.
     */
    fun clearNavigationEvent() {
        _uiState.value = _uiState.value.copy(navigationEvent = null)
    }
    private fun handleOnResume() { /* TODO: Implement */ }
    private fun handleOnPause() { /* TODO: Implement */ }
}
