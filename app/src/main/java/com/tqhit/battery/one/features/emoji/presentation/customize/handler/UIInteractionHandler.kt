package com.tqhit.battery.one.features.emoji.presentation.customize.handler

import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Handles UI interaction events for the emoji customization feature.
 * 
 * Responsibilities:
 * - Color picker interactions
 * - Position selector interactions
 * - Permission and feature management
 * - Error handling and validation
 * - Navigation events
 * 
 * Follows the stats module architecture pattern with comprehensive logging.
 */
class UIInteractionHandler @Inject constructor(
    private val loadCustomizationUseCase: LoadCustomizationUseCase,
    private val saveCustomizationUseCase: SaveCustomizationUseCase
) {
    
    companion object {
        private const val TAG = "EmojiUIHandler"
    }
    
    // Color Picker Interactions
    
    fun handleOpenColorPicker(uiState: MutableStateFlow<CustomizeState>) {
        BatteryLogger.d(TAG, "EMOJI_UI: Open color picker")
        uiState.value = uiState.value.withUIState(showColorPicker = true)
    }
    
    fun handleCloseColorPicker(uiState: MutableStateFlow<CustomizeState>) {
        BatteryLogger.d(TAG, "EMOJI_UI: Close color picker")
        uiState.value = uiState.value.withUIState(showColorPicker = false)
    }
    
    // Position Selector Interactions
    
    fun handleOpenPositionSelector(uiState: MutableStateFlow<CustomizeState>) {
        BatteryLogger.d(TAG, "EMOJI_UI: Open position selector")
        uiState.value = uiState.value.withUIState(showPositionSelector = true)
    }
    
    fun handleClosePositionSelector(uiState: MutableStateFlow<CustomizeState>) {
        BatteryLogger.d(TAG, "EMOJI_UI: Close position selector")
        uiState.value = uiState.value.withUIState(showPositionSelector = false)
    }
    
    // Permission and Feature Management
    
    fun handleRequestPermissions() {
        BatteryLogger.d(TAG, "EMOJI_PERMISSIONS: Request permissions")
        // This will be handled by the fragment
    }
    
    fun handleEnableAccessibilityService() {
        BatteryLogger.d(TAG, "EMOJI_PERMISSIONS: Enable accessibility service")
        // This will be handled by the fragment
    }
    
    fun handleToggleFeatureEnabled(
        enabled: Boolean,
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope
    ) {
        BatteryLogger.d(TAG, "EMOJI_FEATURE: Toggle feature enabled: $enabled")
        
        coroutineScope.launch {
            try {
                val result = saveCustomizationUseCase.setFeatureEnabled(enabled)
                if (result.isFailure) {
                    val exception = result.exceptionOrNull()
                    if (exception != null) {
                        BatteryLogger.e(TAG, "EMOJI_FEATURE: Failed to toggle feature", exception)
                    } else {
                        BatteryLogger.w(TAG, "EMOJI_FEATURE: Failed to toggle feature - no exception details")
                    }
                    uiState.value = uiState.value.withError("Failed to toggle feature: ${exception?.message}")
                }
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "EMOJI_FEATURE: Exception toggling feature", e)
                uiState.value = uiState.value.withError("Error toggling feature: ${e.message}")
            }
        }
    }
    
    // Error Handling
    
    fun handleRetryOperation(
        uiState: MutableStateFlow<CustomizeState>,
        onLoadInitialData: () -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_ERROR: Retry operation")
        uiState.value = uiState.value.withClearedErrors()
        onLoadInitialData()
    }
    
    fun handleDismissError(uiState: MutableStateFlow<CustomizeState>) {
        BatteryLogger.d(TAG, "EMOJI_ERROR: Dismiss error")
        uiState.value = uiState.value.withClearedErrors()
    }
    
    fun handleValidateConfiguration(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope
    ) {
        BatteryLogger.d(TAG, "EMOJI_VALIDATE: Validate configuration")
        
        coroutineScope.launch {
            try {
                val issues = loadCustomizationUseCase.validateCurrentConfiguration()
                if (issues.isNotEmpty()) {
                    uiState.value = uiState.value.withError(validationErrors = issues)
                } else {
                    BatteryLogger.d(TAG, "EMOJI_VALIDATE: Configuration is valid")
                }
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "EMOJI_VALIDATE: Exception validating configuration", e)
                uiState.value = uiState.value.withError("Validation error: ${e.message}")
            }
        }
    }
    
    // Navigation Events (handled by fragment)
    
    fun handleNavigateBack() {
        BatteryLogger.d(TAG, "EMOJI_NAVIGATION: Navigate back")
    }
    
    fun handleNavigateToSettings() {
        BatteryLogger.d(TAG, "EMOJI_NAVIGATION: Navigate to settings")
    }
    
    fun handleNavigateToHelp() {
        BatteryLogger.d(TAG, "EMOJI_NAVIGATION: Navigate to help")
    }
    
    // Lifecycle Events
    
    fun handleScreenEnter(onLoadInitialData: () -> Unit) {
        BatteryLogger.d(TAG, "EMOJI_LIFECYCLE: Screen enter")
        onLoadInitialData()
    }
    
    fun handleScreenExit(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope,
        onSaveConfiguration: suspend () -> Result<Unit>
    ) {
        BatteryLogger.d(TAG, "EMOJI_LIFECYCLE: Screen exit")
        // Save any pending changes
        val currentState = uiState.value
        if (currentState.hasUnsavedChanges && currentState.canSave()) {
            BatteryLogger.d(TAG, "EMOJI_LIFECYCLE: Auto-saving changes on exit")
            coroutineScope.launch {
                onSaveConfiguration()
            }
        }
    }
    
    fun handleResume(onLoadInitialData: () -> Unit) {
        BatteryLogger.d(TAG, "EMOJI_LIFECYCLE: Resume")
        // Refresh data and check permissions
        onLoadInitialData()
    }
    
    fun handlePause(onCancelJobs: () -> Unit) {
        BatteryLogger.d(TAG, "EMOJI_LIFECYCLE: Pause")
        // Cancel any pending operations
        onCancelJobs()
    }
}
