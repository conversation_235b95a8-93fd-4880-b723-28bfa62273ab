package com.tqhit.battery.one.features.emoji.domain.repository
import com.tqhit.battery.one.utils.BatteryLogger

import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.model.UserPreferences
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for managing user customization data.
 * 
 * This repository handles persistence and retrieval of user customization settings
 * for the emoji battery feature. It provides reactive data streams using Flow
 * and follows the established repository pattern from the stats module.
 * 
 * Key responsibilities:
 * - Persist and retrieve customization configurations
 * - Manage user preferences and settings
 * - Track usage history and analytics
 * - Provide reactive data streams for UI updates
 * - Handle data validation and migration
 * 
 * Implementation notes:
 * - Uses DataStore for modern, type-safe persistence
 * - Provides Flow-based reactive data access
 * - Handles data validation and error recovery
 * - Supports atomic updates and transactions
 * - Maintains backward compatibility
 */
interface CustomizationRepository {
    
    /**
     * Reactive stream of the current user customization state.
     * Emits updates whenever any customization data changes.
     * 
     * @return Flow of UserCustomization representing complete user state
     */
    val userCustomizationFlow: Flow<UserCustomization>
    
    /**
     * Reactive stream of the current customization configuration.
     * Emits updates when configuration changes.
     * 
     * @return Flow of CustomizationConfig representing current configuration
     */
    val customizationConfigFlow: Flow<CustomizationConfig>
    
    /**
     * Reactive stream of user preferences.
     * Emits updates when preferences change.
     * 
     * @return Flow of UserPreferences representing current preferences
     */
    val userPreferencesFlow: Flow<UserPreferences>
    
    /**
     * Gets the current customization configuration synchronously.
     * Use this for immediate access when reactive streams are not needed.
     * 
     * @return Current CustomizationConfig or default if none exists
     */
    suspend fun getCurrentCustomizationConfig(): CustomizationConfig
    
    /**
     * Gets the current complete user customization state synchronously.
     * 
     * @return Current UserCustomization or default if none exists
     */
    suspend fun getCurrentUserCustomization(): UserCustomization
    
    /**
     * Saves a new customization configuration.
     * Updates the configuration and triggers reactive stream updates.
     * 
     * @param config The CustomizationConfig to save
     * @return Result indicating success or failure with error details
     */
    suspend fun saveCustomizationConfig(config: CustomizationConfig): Result<Unit>
    
    /**
     * Updates the complete user customization state.
     * Use this for bulk updates or when updating multiple fields.
     * 
     * @param userCustomization The UserCustomization to save
     * @return Result indicating success or failure with error details
     */
    suspend fun saveUserCustomization(userCustomization: UserCustomization): Result<Unit>
    
    /**
     * Updates user preferences only.
     * More efficient than updating complete user customization.
     * 
     * @param preferences The UserPreferences to save
     * @return Result indicating success or failure with error details
     */
    suspend fun saveUserPreferences(preferences: UserPreferences): Result<Unit>
    
    /**
     * Updates permission states in the user customization.
     * Called when permission states change.
     * 
     * @param hasAccessibilityPermission Whether accessibility permission is granted
     * @param hasOverlayPermission Whether overlay permission is granted
     * @param isServiceEnabled Whether accessibility service is enabled
     * @return Result indicating success or failure with error details
     */
    suspend fun updatePermissionStates(
        hasAccessibilityPermission: Boolean,
        hasOverlayPermission: Boolean,
        isServiceEnabled: Boolean
    ): Result<Unit>
    
    /**
     * Enables or disables the emoji battery feature.
     * Updates the configuration and permission checks.
     * 
     * @param enabled Whether the feature should be enabled
     * @return Result indicating success or failure with error details
     */
    suspend fun setFeatureEnabled(enabled: Boolean): Result<Unit>
    
    /**
     * Records usage of a specific style for analytics.
     * Updates usage history and favorite styles.
     * 
     * @param styleId The ID of the style that was used
     * @return Result indicating success or failure with error details
     */
    suspend fun recordStyleUsage(styleId: String): Result<Unit>
    
    /**
     * Resets all customization data to defaults.
     * Useful for troubleshooting or user preference.
     * 
     * @param preservePreferences Whether to keep user preferences (default: true)
     * @return Result indicating success or failure with error details
     */
    suspend fun resetToDefaults(preservePreferences: Boolean = true): Result<Unit>
    
    /**
     * Clears all stored customization data.
     * More aggressive than reset - removes everything.
     * 
     * @return Result indicating success or failure with error details
     */
    suspend fun clearAllData(): Result<Unit>
    
    /**
     * Validates the current configuration and fixes any issues.
     * Useful for data migration or corruption recovery.
     * 
     * @return Result with validation results and any fixes applied
     */
    suspend fun validateAndFixConfiguration(): Result<List<String>>
    
    /**
     * Exports current customization data for backup or sharing.
     * Returns serialized data that can be imported later.
     * 
     * @return Result with serialized customization data
     */
    suspend fun exportCustomizationData(): Result<String>
    
    /**
     * Imports customization data from backup or sharing.
     * Validates and applies the imported configuration.
     * 
     * @param serializedData The serialized customization data to import
     * @param overwriteExisting Whether to overwrite existing data (default: false)
     * @return Result indicating success or failure with import details
     */
    suspend fun importCustomizationData(
        serializedData: String,
        overwriteExisting: Boolean = false
    ): Result<Unit>
}

/**
 * Exception types for CustomizationRepository operations.
 */
sealed class CustomizationRepositoryException(message: String, cause: Throwable? = null) : Exception(message, cause) {
    
    /**
     * Thrown when data validation fails.
     */
    class ValidationException(message: String, cause: Throwable? = null) : CustomizationRepositoryException(message, cause)
    
    /**
     * Thrown when data persistence fails.
     */
    class PersistenceException(message: String, cause: Throwable? = null) : CustomizationRepositoryException(message, cause)
    
    /**
     * Thrown when data serialization/deserialization fails.
     */
    class SerializationException(message: String, cause: Throwable? = null) : CustomizationRepositoryException(message, cause)
    
    /**
     * Thrown when data migration fails.
     */
    class MigrationException(message: String, cause: Throwable? = null) : CustomizationRepositoryException(message, cause)
}
