package com.tqhit.battery.one.features.emoji.presentation.customize.manager

import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Manages data loading and observation for emoji customization.
 * 
 * Responsibilities:
 * - Load initial customization data
 * - Observe data changes from use cases
 * - Observe battery status for live preview
 * - Handle data loading states and errors
 * 
 * Follows the stats module architecture pattern with comprehensive logging.
 */
class DataManager @Inject constructor(
    private val loadCustomizationUseCase: LoadCustomizationUseCase,
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) {
    
    companion object {
        private const val TAG = "EmojiDataManager"
    }
    
    // Job for managing data loading
    private var dataLoadingJob: Job? = null
    
    /**
     * Starts observing data changes from use cases and battery provider.
     */
    fun startDataObservation(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope,
        onBatteryLevelChanged: (Int, Boolean) -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_DATA_OBSERVATION: Starting data observation")
        
        // Observe enriched customization data
        coroutineScope.launch {
            loadCustomizationUseCase.userCustomizationFlow
                .catch { exception ->
                    BatteryLogger.e(TAG, "EMOJI_DATA_OBSERVATION: Error in customization flow", exception)
                    uiState.value = uiState.value.withError("Failed to load customization data")
                }
                .collect { enrichedCustomization ->
                    BatteryLogger.d(TAG, "EMOJI_DATA_OBSERVATION: Received enriched customization update")
                    uiState.value = uiState.value.withEnrichedCustomization(enrichedCustomization)
                }
        }
        
        // Observe battery status for live preview
        coroutineScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .catch { exception ->
                    BatteryLogger.e(TAG, "EMOJI_DATA_OBSERVATION: Error in battery status flow", exception)
                }
                .collect { batteryStatus ->
                    val currentState = uiState.value
                    if (currentState.showLivePreview && batteryStatus != null) {
                        BatteryLogger.d(TAG, "EMOJI_BATTERY_UPDATE: Live preview battery update: ${batteryStatus.percentage}%, charging: ${batteryStatus.isCharging}")
                        onBatteryLevelChanged(batteryStatus.percentage, batteryStatus.isCharging)
                    }
                }
        }
    }
    
    /**
     * Loads initial data for the screen.
     */
    fun loadInitialData(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope,
        onEventCallback: (CustomizeEvent) -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_LOAD_INITIAL: Loading initial data")
        
        dataLoadingJob?.cancel()
        dataLoadingJob = coroutineScope.launch {
            try {
                uiState.value = uiState.value.withLoadingState(isLoading = true, isLoadingStyles = true)
                
                // Load current customization and available styles
                val enrichedCustomization = loadCustomizationUseCase.getCurrentEnrichedUserCustomization()
                
                uiState.value = uiState.value
                    .withEnrichedCustomization(enrichedCustomization)
                    .withLoadingState(isLoading = false, isLoadingStyles = false)
                
                BatteryLogger.d(TAG, "EMOJI_LOAD_INITIAL: Initial data loaded successfully")
                onEventCallback(CustomizeEvent.CustomizationDataLoaded(true))
                onEventCallback(CustomizeEvent.StylesDataLoaded(true))
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "EMOJI_LOAD_INITIAL: Failed to load initial data", e)
                uiState.value = uiState.value.withError("Failed to load customization data: ${e.message}")
                onEventCallback(CustomizeEvent.CustomizationDataLoaded(false))
                onEventCallback(CustomizeEvent.StylesDataLoaded(false))
            }
        }
    }
    
    /**
     * Handles customization data loaded event.
     */
    fun handleCustomizationDataLoaded(
        success: Boolean,
        uiState: MutableStateFlow<CustomizeState>
    ) {
        BatteryLogger.d(TAG, "EMOJI_DATA: Customization data loaded: $success")
        if (!success) {
            uiState.value = uiState.value.withError("Failed to load customization data")
        }
    }
    
    /**
     * Handles styles data loaded event.
     */
    fun handleStylesDataLoaded(
        success: Boolean,
        uiState: MutableStateFlow<CustomizeState>
    ) {
        BatteryLogger.d(TAG, "EMOJI_DATA: Styles data loaded: $success")
        if (!success) {
            uiState.value = uiState.value.withError("Failed to load styles data")
        }
    }
    
    /**
     * Handles configuration saved event.
     */
    fun handleConfigurationSaved(uiState: MutableStateFlow<CustomizeState>) {
        BatteryLogger.d(TAG, "EMOJI_DATA: Configuration saved successfully")
        uiState.value = uiState.value.copy(hasUnsavedChanges = false, isConfigModified = false)
    }
    
    /**
     * Handles configuration save failed event.
     */
    fun handleConfigurationSaveFailed(
        error: String,
        uiState: MutableStateFlow<CustomizeState>
    ) {
        BatteryLogger.e(TAG, "EMOJI_DATA: Configuration save failed: $error")
        uiState.value = uiState.value.withError("Save failed: $error")
    }
    
    /**
     * Cancels data loading job.
     */
    fun cancelDataLoading() {
        BatteryLogger.d(TAG, "EMOJI_LIFECYCLE: Cancelling data loading job")
        dataLoadingJob?.cancel()
    }
}
