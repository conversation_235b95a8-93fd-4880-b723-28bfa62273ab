package com.tqhit.battery.one.features.emoji.domain.model
import com.tqhit.battery.one.utils.BatteryLogger

/**
 * Complete user customization state for the emoji battery feature.
 * Represents the full user experience state including permissions, preferences, and history.
 * 
 * This model encapsulates:
 * - Current customization configuration
 * - Permission states required for the feature
 * - User preferences and settings
 * - Usage history and analytics data
 * 
 * @param customizationConfig Current active customization configuration
 * @param hasAccessibilityPermission Whether accessibility service permission is granted
 * @param hasOverlayPermission Whether system overlay permission is granted (Android < 8.0)
 * @param isAccessibilityServiceEnabled Whether the accessibility service is currently running
 * @param userPreferences User preferences for the feature
 * @param usageHistory Usage history and analytics data
 */
data class UserCustomization(
    val customizationConfig: CustomizationConfig = CustomizationConfig.createDefault(),
    val hasAccessibilityPermission: Boolean = false,
    val hasOverlayPermission: Boolean = false,
    val isAccessibilityServiceEnabled: Boolean = false,
    val userPreferences: UserPreferences = UserPreferences(),
    val usageHistory: UsageHistory = UsageHistory()
) {
    
    /**
     * Checks if all required permissions are granted for the feature to work.
     * 
     * @return true if all necessary permissions are available
     */
    fun hasAllRequiredPermissions(): Boolean {
        return hasAccessibilityPermission && 
               (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O || hasOverlayPermission)
    }
    
    /**
     * Checks if the feature is ready to be used.
     * Requires permissions, service enabled, and valid configuration.
     * 
     * @return true if feature is fully operational
     */
    fun isFeatureReady(): Boolean {
        return hasAllRequiredPermissions() &&
               isAccessibilityServiceEnabled &&
               customizationConfig.isValid() &&
               customizationConfig.isFeatureEnabled
    }
    
    /**
     * Gets the current feature status for UI display.
     * 
     * @return FeatureStatus indicating current state
     */
    fun getFeatureStatus(): FeatureStatus {
        return when {
            !hasAllRequiredPermissions() -> FeatureStatus.PERMISSIONS_REQUIRED
            !isAccessibilityServiceEnabled -> FeatureStatus.SERVICE_DISABLED
            !customizationConfig.isValid() -> FeatureStatus.CONFIGURATION_INVALID
            !customizationConfig.isFeatureEnabled -> FeatureStatus.FEATURE_DISABLED
            else -> FeatureStatus.ACTIVE
        }
    }
    
    /**
     * Creates a copy with updated customization configuration.
     * 
     * @param newConfig The new customization configuration
     * @return Updated UserCustomization
     */
    fun withCustomizationConfig(newConfig: CustomizationConfig): UserCustomization {
        return copy(
            customizationConfig = newConfig,
            usageHistory = usageHistory.withConfigurationChange()
        )
    }
    
    /**
     * Creates a copy with updated permission states.
     * 
     * @param accessibility Accessibility permission state
     * @param overlay Overlay permission state (optional)
     * @param serviceEnabled Service enabled state
     * @return Updated UserCustomization
     */
    fun withPermissions(
        accessibility: Boolean,
        overlay: Boolean = hasOverlayPermission,
        serviceEnabled: Boolean = isAccessibilityServiceEnabled
    ): UserCustomization {
        return copy(
            hasAccessibilityPermission = accessibility,
            hasOverlayPermission = overlay,
            isAccessibilityServiceEnabled = serviceEnabled
        )
    }
    
    companion object {
        /**
         * Creates a default UserCustomization state.
         * Used for new users or when resetting to defaults.
         * 
         * @return Default UserCustomization
         */
        fun createDefault(): UserCustomization {
            return UserCustomization()
        }
    }
}

/**
 * User preferences for the emoji battery feature.
 * Contains settings that affect user experience but not core functionality.
 * 
 * @param showOnboardingTips Whether to show onboarding tips and hints
 * @param enableHapticFeedback Whether to provide haptic feedback for interactions
 * @param autoSaveChanges Whether to automatically save configuration changes
 * @param showPreviewInGallery Whether to show live preview in gallery
 * @param preferredColorPalette User's preferred color palette for customization
 */
data class UserPreferences(
    val showOnboardingTips: Boolean = true,
    val enableHapticFeedback: Boolean = true,
    val autoSaveChanges: Boolean = true,
    val showPreviewInGallery: Boolean = true,
    val preferredColorPalette: ColorPalette = ColorPalette.DEFAULT
) {
    
    companion object {
        /**
         * Creates default user preferences.
         * 
         * @return Default UserPreferences
         */
        fun createDefault(): UserPreferences {
            return UserPreferences()
        }
    }
}

/**
 * Usage history and analytics data for the emoji battery feature.
 * Tracks user engagement and feature usage patterns.
 * 
 * @param totalConfigurationChanges Number of times user has changed configuration
 * @param totalStylesUsed Number of different styles user has tried
 * @param featureEnabledDurationMs Total time feature has been enabled (milliseconds)
 * @param lastUsedTimestamp When the feature was last actively used
 * @param favoriteStyleIds List of style IDs user uses most frequently
 */
data class UsageHistory(
    val totalConfigurationChanges: Int = 0,
    val totalStylesUsed: Int = 0,
    val featureEnabledDurationMs: Long = 0L,
    val lastUsedTimestamp: Long = 0L,
    val favoriteStyleIds: List<String> = emptyList()
) {
    
    /**
     * Creates a copy with incremented configuration change count.
     * 
     * @return Updated UsageHistory
     */
    fun withConfigurationChange(): UsageHistory {
        return copy(
            totalConfigurationChanges = totalConfigurationChanges + 1,
            lastUsedTimestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * Creates a copy with new style usage recorded.
     * 
     * @param styleId The style ID that was used
     * @return Updated UsageHistory
     */
    fun withStyleUsed(styleId: String): UsageHistory {
        val newFavorites = if (styleId !in favoriteStyleIds) {
            (favoriteStyleIds + styleId).takeLast(10) // Keep last 10 favorites
        } else {
            favoriteStyleIds
        }
        
        return copy(
            totalStylesUsed = if (styleId !in favoriteStyleIds) totalStylesUsed + 1 else totalStylesUsed,
            lastUsedTimestamp = System.currentTimeMillis(),
            favoriteStyleIds = newFavorites
        )
    }
}

/**
 * Color palette options for user customization.
 */
enum class ColorPalette(val displayName: String) {
    DEFAULT("Default"),
    VIBRANT("Vibrant"),
    PASTEL("Pastel"),
    MONOCHROME("Monochrome"),
    NEON("Neon");
    
    companion object {
        /**
         * Gets the default color palette.
         * 
         * @return Default ColorPalette
         */
        fun getDefault(): ColorPalette = DEFAULT
    }
}

/**
 * Feature status enumeration for UI state management.
 */
enum class FeatureStatus(val displayName: String, val isOperational: Boolean) {
    ACTIVE("Active", true),
    FEATURE_DISABLED("Feature Disabled", false),
    PERMISSIONS_REQUIRED("Permissions Required", false),
    SERVICE_DISABLED("Service Disabled", false),
    CONFIGURATION_INVALID("Configuration Invalid", false);
    
    /**
     * Gets user-friendly description of the status.
     * 
     * @return Description string for UI display
     */
    fun getDescription(): String {
        return when (this) {
            ACTIVE -> "Emoji battery is active and working"
            FEATURE_DISABLED -> "Feature is disabled. Enable in settings."
            PERMISSIONS_REQUIRED -> "Required permissions not granted"
            SERVICE_DISABLED -> "Accessibility service is not enabled"
            CONFIGURATION_INVALID -> "Configuration is incomplete or invalid"
        }
    }
}
