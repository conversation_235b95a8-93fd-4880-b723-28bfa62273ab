package com.tqhit.battery.one.features.emoji.presentation.gallery.adapter
import com.tqhit.battery.one.utils.BatteryLogger

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ItemBatteryStyleBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle

/**
 * RecyclerView adapter for displaying battery styles in a grid layout.
 * 
 * This adapter follows the established stats module architecture pattern:
 * - Uses ListAdapter with DiffUtil for efficient updates
 * - Integrates with Glide for image loading with error handling
 * - Supports premium indicators and badges
 * - Handles missing emoji assets gracefully
 * - Provides click listeners for various user interactions
 * - Implements proper ViewHolder pattern with ViewBinding
 * - Includes comprehensive logging for debugging
 */
class BatteryStyleAdapter(
    private val onStyleClick: (BatteryStyle) -> Unit,
    private val onStyleLongClick: (BatteryStyle) -> Unit,
    private val onPremiumUnlockClick: (BatteryStyle) -> Unit,
    private val onImageLoadError: (String, String) -> Unit
) : ListAdapter<BatteryStyle, BatteryStyleAdapter.BatteryStyleViewHolder>(BatteryStyleDiffCallback()) {
    
    companion object {
        private const val TAG = "BatteryStyleAdapter"
        private const val PLACEHOLDER_EMOJI_URL = "https://emoji.aranja.com/static/emoji-data/img-apple-160/1f600.png"
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BatteryStyleViewHolder {
        val binding = ItemBatteryStyleBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return BatteryStyleViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: BatteryStyleViewHolder, position: Int) {
        val style = getItem(position)
        holder.bind(style)
    }

    /**
     * Public wrapper for getItem to make it accessible for testing.
     */
    public override fun getItem(position: Int): BatteryStyle {
        return super.getItem(position)
    }
    
    /**
     * ViewHolder for battery style items.
     */
    inner class BatteryStyleViewHolder(
        internal val binding: ItemBatteryStyleBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(style: BatteryStyle) {
            BatteryLogger.d(TAG, "BIND: Binding style: ${style.name} (ID: ${style.id})")
            
            bindBasicInfo(style)
            bindImages(style)
            bindBadges(style)
            bindClickListeners(style)
            bindActionButton(style)
        }
        
        /**
         * Binds basic style information (name, category).
         */
        private fun bindBasicInfo(style: BatteryStyle) {
            binding.styleNameText.text = style.name
            
            val categoryText = buildString {
                append(style.category.displayName)
                append(" • ")
                append(if (style.isPremium) "Premium" else "Free")
            }
            binding.categoryText.text = categoryText
            
            // Set preview percentage (could be dynamic based on current battery level)
            binding.percentagePreview.text = "85%"
        }
        
        /**
         * Binds battery and emoji images using Glide.
         */
        private fun bindImages(style: BatteryStyle) {
            BatteryLogger.d(TAG, "BIND_IMAGES: Loading images for style: ${style.id}")
            
            // Show loading indicator
            binding.imageLoadingIndicator.visibility = View.VISIBLE
            binding.imageErrorIndicator.visibility = View.GONE
            
            var batteryImageLoaded = false
            var emojiImageLoaded = false
            
            fun checkAllImagesLoaded() {
                if (batteryImageLoaded && emojiImageLoaded) {
                    binding.imageLoadingIndicator.visibility = View.GONE
                    BatteryLogger.d(TAG, "IMAGES_LOADED: All images loaded for style: ${style.id}")
                }
            }
            
            // Load battery image
            loadBatteryImage(style) { success ->
                batteryImageLoaded = true
                if (!success) {
                    binding.imageErrorIndicator.visibility = View.VISIBLE
                    binding.imageLoadingIndicator.visibility = View.GONE
                    onImageLoadError(style.id, "battery")
                } else {
                    checkAllImagesLoaded()
                }
            }
            
            // Load emoji image
            loadEmojiImage(style) { success ->
                emojiImageLoaded = true
                if (!success) {
                    binding.imageErrorIndicator.visibility = View.VISIBLE
                    binding.imageLoadingIndicator.visibility = View.GONE
                    onImageLoadError(style.id, "emoji")
                } else {
                    checkAllImagesLoaded()
                }
            }
        }
        
        /**
         * Loads the battery image using Glide.
         */
        private fun loadBatteryImage(style: BatteryStyle, onComplete: (Boolean) -> Unit) {
            Glide.with(binding.batteryImageView.context)
                .load(style.batteryImageUrl)
                .placeholder(R.drawable.ic_battery_placeholder)
                .error(R.drawable.ic_battery_error)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .transition(DrawableTransitionOptions.withCrossFade())
                .listener(object : com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable> {
                    override fun onLoadFailed(
                        e: com.bumptech.glide.load.engine.GlideException?,
                        model: Any?,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        if (e != null) {
                            BatteryLogger.w(TAG, "BATTERY_IMAGE_FAILED: Failed to load battery image for style: ${style.id}", e)
                        } else {
                            BatteryLogger.w(TAG, "BATTERY_IMAGE_FAILED: Failed to load battery image for style: ${style.id} - no exception details")
                        }
                        onComplete(false)
                        return false
                    }

                    override fun onResourceReady(
                        resource: android.graphics.drawable.Drawable,
                        model: Any,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>,
                        dataSource: com.bumptech.glide.load.DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        BatteryLogger.d(TAG, "BATTERY_IMAGE_SUCCESS: Battery image loaded for style: ${style.id}")
                        onComplete(true)
                        return false
                    }
                })
                .into(binding.batteryImageView)
        }
        
        /**
         * Loads the emoji image using Glide with fallback to placeholder.
         */
        private fun loadEmojiImage(style: BatteryStyle, onComplete: (Boolean) -> Unit) {
            // Use placeholder URL for testing if the original URL fails
            val imageUrl = if (style.emojiImageUrl.contains("example.com")) {
                PLACEHOLDER_EMOJI_URL
            } else {
                style.emojiImageUrl
            }
            
            Glide.with(binding.emojiImageView.context)
                .load(imageUrl)
                .placeholder(R.drawable.ic_emoji_placeholder)
                .error(R.drawable.ic_emoji_error)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .transition(DrawableTransitionOptions.withCrossFade())
                .listener(object : com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable> {
                    override fun onLoadFailed(
                        e: com.bumptech.glide.load.engine.GlideException?,
                        model: Any?,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        if (e != null) {
                            BatteryLogger.w(TAG, "EMOJI_IMAGE_FAILED: Failed to load emoji image for style: ${style.id}", e)
                        } else {
                            BatteryLogger.w(TAG, "EMOJI_IMAGE_FAILED: Failed to load emoji image for style: ${style.id} - no exception details")
                        }

                        // Try fallback to placeholder URL if original failed
                        if (imageUrl != PLACEHOLDER_EMOJI_URL) {
                            BatteryLogger.d(TAG, "EMOJI_FALLBACK: Trying placeholder URL for style: ${style.id}")
                            loadEmojiImageFallback(style, onComplete)
                            return true // Prevent error drawable from showing
                        }

                        onComplete(false)
                        return false
                    }

                    override fun onResourceReady(
                        resource: android.graphics.drawable.Drawable,
                        model: Any,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>,
                        dataSource: com.bumptech.glide.load.DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        BatteryLogger.d(TAG, "EMOJI_IMAGE_SUCCESS: Emoji image loaded for style: ${style.id}")
                        onComplete(true)
                        return false
                    }
                })
                .into(binding.emojiImageView)
        }
        
        /**
         * Loads emoji image with fallback placeholder URL.
         */
        private fun loadEmojiImageFallback(style: BatteryStyle, onComplete: (Boolean) -> Unit) {
            Glide.with(binding.emojiImageView.context)
                .load(PLACEHOLDER_EMOJI_URL)
                .placeholder(R.drawable.ic_emoji_placeholder)
                .error(R.drawable.ic_emoji_error)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .listener(object : com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable> {
                    override fun onLoadFailed(
                        e: com.bumptech.glide.load.engine.GlideException?,
                        model: Any?,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        if (e != null) {
                            BatteryLogger.w(TAG, "EMOJI_FALLBACK_FAILED: Fallback emoji image failed for style: ${style.id}", e)
                        } else {
                            BatteryLogger.w(TAG, "EMOJI_FALLBACK_FAILED: Fallback emoji image failed for style: ${style.id} - no exception details")
                        }
                        onComplete(false)
                        return false
                    }

                    override fun onResourceReady(
                        resource: android.graphics.drawable.Drawable,
                        model: Any,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>,
                        dataSource: com.bumptech.glide.load.DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        BatteryLogger.d(TAG, "EMOJI_FALLBACK_SUCCESS: Fallback emoji image loaded for style: ${style.id}")
                        onComplete(true)
                        return false
                    }
                })
                .into(binding.emojiImageView)
        }
        
        /**
         * Binds premium and popular badges.
         */
        private fun bindBadges(style: BatteryStyle) {
            binding.premiumBadge.visibility = if (style.isPremium) View.VISIBLE else View.GONE
            binding.popularBadge.visibility = if (style.isPopular) View.VISIBLE else View.GONE
        }
        
        /**
         * Binds click listeners for the item.
         */
        private fun bindClickListeners(style: BatteryStyle) {
            binding.styleCard.setOnClickListener {
                BatteryLogger.d(TAG, "CLICK: Style card clicked: ${style.name}")
                onStyleClick(style)
            }
            
            binding.styleCard.setOnLongClickListener {
                BatteryLogger.d(TAG, "LONG_CLICK: Style card long clicked: ${style.name}")
                onStyleLongClick(style)
                true
            }
        }
        
        /**
         * Binds action button for premium styles.
         */
        private fun bindActionButton(style: BatteryStyle) {
            if (style.isPremium) {
                binding.actionButton.visibility = View.VISIBLE
                binding.actionButton.text = "Unlock"
                binding.actionButton.setOnClickListener {
                    BatteryLogger.d(TAG, "PREMIUM_UNLOCK: Premium unlock clicked for style: ${style.name}")
                    onPremiumUnlockClick(style)
                }
            } else {
                binding.actionButton.visibility = View.GONE
            }
        }
    }
    
    /**
     * DiffUtil callback for efficient list updates.
     */
    private class BatteryStyleDiffCallback : DiffUtil.ItemCallback<BatteryStyle>() {
        override fun areItemsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
            return oldItem == newItem
        }
    }
}
