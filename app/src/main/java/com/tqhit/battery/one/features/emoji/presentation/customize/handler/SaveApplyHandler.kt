package com.tqhit.battery.one.features.emoji.presentation.customize.handler

import com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Handles save and apply operations for the emoji customization feature.
 * 
 * Responsibilities:
 * - Save configuration
 * - Apply and enable feature
 * - Reset to defaults
 * - Discard changes
 * 
 * Follows the stats module architecture pattern with comprehensive logging.
 */
class SaveApplyHandler @Inject constructor(
    private val saveCustomizationUseCase: SaveCustomizationUseCase,
    private val resetCustomizationUseCase: ResetCustomizationUseCase
) {
    
    companion object {
        private const val TAG = "EmojiSaveHandler"
    }
    
    /**
     * Saves the current configuration.
     */
    fun handleSaveConfiguration(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope,
        onSaveComplete: suspend () -> Result<Unit>
    ) {
        BatteryLogger.d(TAG, "EMOJI_SAVE: Save configuration requested")
        
        coroutineScope.launch {
            onSaveComplete()
        }
    }
    
    /**
     * Applies the configuration and enables the feature.
     */
    fun handleApplyAndEnable(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope,
        onSaveComplete: suspend () -> Result<Unit>,
        onEventCallback: (CustomizeEvent) -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_APPLY: Apply and enable requested")
        
        coroutineScope.launch {
            try {
                uiState.value = uiState.value.withLoadingState(isSaving = true)
                
                // Save current configuration
                val saveResult = onSaveComplete()
                if (saveResult.isFailure) {
                    return@launch
                }
                
                // Enable the feature
                val enableResult = saveCustomizationUseCase.setFeatureEnabled(true)
                if (enableResult.isSuccess) {
                    BatteryLogger.d(TAG, "EMOJI_APPLY: Successfully applied and enabled feature")
                    uiState.value = uiState.value.withLoadingState(isSaving = false)
                    onEventCallback(CustomizeEvent.ConfigurationSaved)
                } else {
                    val exception = enableResult.exceptionOrNull()
                    if (exception != null) {
                        BatteryLogger.e(TAG, "EMOJI_APPLY: Failed to enable feature", exception)
                    } else {
                        BatteryLogger.w(TAG, "EMOJI_APPLY: Failed to enable feature - no exception details")
                    }
                    uiState.value = uiState.value.withError("Failed to enable feature: ${exception?.message}")
                }
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "EMOJI_APPLY: Exception applying and enabling", e)
                uiState.value = uiState.value.withError("Error applying changes: ${e.message}")
            }
        }
    }
    
    /**
     * Handles reset to defaults request (shows confirmation).
     */
    fun handleResetToDefaults() {
        BatteryLogger.d(TAG, "EMOJI_RESET: Reset to defaults requested")
        // This should show confirmation dialog in UI
    }
    
    /**
     * Confirms and executes the reset operation.
     */
    fun handleConfirmReset(
        uiState: MutableStateFlow<CustomizeState>,
        coroutineScope: CoroutineScope,
        onLoadInitialData: () -> Unit
    ) {
        BatteryLogger.d(TAG, "EMOJI_RESET: Reset confirmed")
        
        coroutineScope.launch {
            try {
                uiState.value = uiState.value.withLoadingState(isLoading = true)
                
                val result = resetCustomizationUseCase.resetCustomizationConfig()
                if (result.isSuccess) {
                    BatteryLogger.d(TAG, "EMOJI_RESET: Successfully reset configuration")
                    uiState.value = uiState.value.withLoadingState(isLoading = false)
                    onLoadInitialData() // Reload to reflect changes
                } else {
                    val exception = result.exceptionOrNull()
                    if (exception != null) {
                        BatteryLogger.e(TAG, "EMOJI_RESET: Failed to reset configuration", exception)
                    } else {
                        BatteryLogger.w(TAG, "EMOJI_RESET: Failed to reset configuration - no exception details")
                    }
                    uiState.value = uiState.value.withError("Failed to reset: ${exception?.message}")
                }
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "EMOJI_RESET: Exception resetting configuration", e)
                uiState.value = uiState.value.withError("Error resetting: ${e.message}")
            }
        }
    }
    
    /**
     * Discards changes and reloads original data.
     */
    fun handleDiscardChanges(onLoadInitialData: () -> Unit) {
        BatteryLogger.d(TAG, "EMOJI_DISCARD: Discard changes requested")
        onLoadInitialData() // Reload original data
    }
    
    /**
     * Performs the actual save operation.
     */
    suspend fun saveCurrentConfiguration(
        uiState: MutableStateFlow<CustomizeState>,
        onEventCallback: (CustomizeEvent) -> Unit
    ): Result<Unit> {
        return try {
            uiState.value = uiState.value.withLoadingState(isSaving = true)
            
            val currentState = uiState.value
            val currentConfig = currentState.currentCustomization.customizationConfig
            val updatedConfig = currentConfig.withStyleConfig(currentState.editingConfig)
            
            val result = saveCustomizationUseCase.saveCustomizationConfig(updatedConfig)
            
            if (result.isSuccess) {
                BatteryLogger.d(TAG, "EMOJI_SAVE: Configuration saved successfully")
                uiState.value = uiState.value.withLoadingState(isSaving = false)
                onEventCallback(CustomizeEvent.ConfigurationSaved)
            } else {
                val exception = result.exceptionOrNull()
                if (exception != null) {
                    BatteryLogger.e(TAG, "EMOJI_SAVE: Failed to save configuration", exception)
                } else {
                    BatteryLogger.w(TAG, "EMOJI_SAVE: Failed to save configuration - no exception details")
                }
                uiState.value = uiState.value.withError("Save failed: ${exception?.message}")
            }
            
            result
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "EMOJI_SAVE: Exception saving configuration", e)
            uiState.value = uiState.value.withError("Save error: ${e.message}")
            Result.failure(e)
        }
    }
}
