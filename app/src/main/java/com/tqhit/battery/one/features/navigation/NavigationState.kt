package com.tqhit.battery.one.features.navigation

import androidx.fragment.app.Fragment
import com.tqhit.battery.one.R

/**
 * Represents the current navigation state based on battery charging status.
 * This data class encapsulates which fragment should be displayed and which
 * navigation items should be visible based on the current charging state.
 *
 * @param activeFragmentId The resource ID of the currently active fragment
 * @param visibleMenuItems List of menu item IDs that should be visible
 * @param isCharging Whether the device is currently charging
 * @param shouldShowTransition Whether to show transition animation
 */
data class NavigationState(
    val activeFragmentId: Int,
    val visibleMenuItems: List<Int>,
    val isCharging: Boolean,
    val shouldShowTransition: Boolean = true
) {
    companion object {
        private const val TAG = "NavigationState"
        
        /**
         * All available menu items in the bottom navigation
         * NOTE: settingsFragment temporarily removed to fix BottomNavigationView 5-item limit
         */
        val ALL_MENU_ITEMS = listOf(
            R.id.chargeFragment,
            R.id.dischargeFragment,
            R.id.healthFragment,
            // R.id.settingsFragment, // TEMPORARILY DISABLED - exceeds 5-item limit
            R.id.animationGridFragment,
            R.id.emojiBatteryFragment
        )

        /**
         * Menu items that are always visible regardless of charging state
         * NOTE: settingsFragment temporarily removed to fix BottomNavigationView 5-item limit
         */
        val ALWAYS_VISIBLE_ITEMS = listOf(
            R.id.healthFragment,
            // R.id.settingsFragment, // TEMPORARILY DISABLED - exceeds 5-item limit
            R.id.animationGridFragment,
            R.id.emojiBatteryFragment
        )
        
        /**
         * Creates a NavigationState for charging mode.
         * Shows charge fragment and hides discharge fragment.
         *
         * @param shouldShowTransition Whether to animate the transition
         * @return NavigationState configured for charging mode
         */
        fun createChargingState(shouldShowTransition: Boolean = true): NavigationState {
            return NavigationState(
                activeFragmentId = R.id.chargeFragment,
                visibleMenuItems = ALWAYS_VISIBLE_ITEMS + R.id.chargeFragment,
                isCharging = true,
                shouldShowTransition = shouldShowTransition
            )
        }
        
        /**
         * Creates a NavigationState for discharging mode.
         * Shows discharge fragment and hides charge fragment.
         *
         * @param shouldShowTransition Whether to animate the transition
         * @return NavigationState configured for discharging mode
         */
        fun createDischargingState(shouldShowTransition: Boolean = true): NavigationState {
            return NavigationState(
                activeFragmentId = R.id.dischargeFragment,
                visibleMenuItems = ALWAYS_VISIBLE_ITEMS + R.id.dischargeFragment,
                isCharging = false,
                shouldShowTransition = shouldShowTransition
            )
        }
        
        /**
         * Creates a default NavigationState when battery status is unknown.
         * Defaults to charging state without transition animation for consistency.
         *
         * @return Default NavigationState
         */
        fun createDefaultState(): NavigationState {
            return createChargingState(shouldShowTransition = false)
        }

        /**
         * Creates a NavigationState for animation mode.
         * Shows animation grid fragment with all menu items visible.
         *
         * @param isCharging Current charging status
         * @param shouldShowTransition Whether to animate the transition
         * @return NavigationState configured for animation mode
         */
        fun createAnimationState(isCharging: Boolean, shouldShowTransition: Boolean = true): NavigationState {
            return NavigationState(
                activeFragmentId = R.id.animationGridFragment,
                //if (isCharging) R.id.chargeFragment else R.id.dischargeFragment,
                //visibleMenuItems = ALL_MENU_ITEMS,
                visibleMenuItems = ALWAYS_VISIBLE_ITEMS + if (isCharging) R.id.chargeFragment else R.id.dischargeFragment,
                isCharging = isCharging,
                shouldShowTransition = shouldShowTransition
            )
        }
    }
    
    /**
     * Checks if this state represents a charging state
     */
    fun isChargingState(): Boolean = isCharging
    
    /**
     * Checks if this state represents a discharging state
     */
    fun isDischargingState(): Boolean = !isCharging
    
    /**
     * Checks if the given menu item should be visible in this state
     */
    fun isMenuItemVisible(menuItemId: Int): Boolean = visibleMenuItems.contains(menuItemId)
    
    /**
     * Gets the fragment class that should be displayed for this state
     */
    fun getFragmentClass(): Class<out Fragment> {
        return when (activeFragmentId) {
            R.id.chargeFragment -> com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment::class.java
            R.id.dischargeFragment -> com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment::class.java
            R.id.healthFragment -> com.tqhit.battery.one.fragment.main.HealthFragment::class.java
            R.id.settingsFragment -> {
                // TEMPORARILY DISABLED: Settings fragment removed from navigation
                // Return animation fragment as fallback
                com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment::class.java
            }
            R.id.animationGridFragment -> com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment::class.java
            R.id.emojiBatteryFragment -> com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment::class.java
            else -> com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment::class.java
        }
    }
    
    /**
     * Creates a new fragment instance for this state
     * @deprecated Use DynamicNavigationManager's fragment caching instead for better performance
     */
    @Deprecated("Use DynamicNavigationManager's fragment caching for better performance")
    fun createFragment(): Fragment {
        return when (activeFragmentId) {
            R.id.chargeFragment -> com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment()
            R.id.dischargeFragment -> com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment()
            R.id.healthFragment -> com.tqhit.battery.one.fragment.main.HealthFragment()
            R.id.settingsFragment -> {
                // TEMPORARILY DISABLED: Settings fragment removed from navigation
                // Return animation fragment as fallback
                com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment()
            }
            R.id.animationGridFragment -> com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment()
            R.id.emojiBatteryFragment -> com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment()
            else -> com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment()
        }
    }
}

/**
 * Represents a navigation state change event.
 * Used to communicate state transitions to observers.
 *
 * @param previousState The previous navigation state (null if this is the initial state)
 * @param newState The new navigation state
 * @param reason The reason for the state change
 */
data class NavigationStateChange(
    val previousState: NavigationState?,
    val newState: NavigationState,
    val reason: StateChangeReason
)

/**
 * Enum representing the reason for a navigation state change
 */
enum class StateChangeReason {
    INITIAL_SETUP,
    CHARGING_STARTED,
    CHARGING_STOPPED,
    USER_NAVIGATION,
    APP_RESUME,
    FRAGMENT_RESTORE
}
