<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/white">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Emoji Feature Test"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Status: Ready"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tv_selected_emoji"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Selected: None"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tv_available_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Available: 0"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Error: None"
        android:textSize="14sp"
        android:textColor="@android:color/holo_red_dark"
        android:visibility="gone"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btn_test_emoji_load"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Load Initial Data"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/btn_test_emoji_selection"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Test Search &amp; Filter"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/btn_test_emoji_display"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Refresh Data"
        android:layout_marginBottom="16dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tv_log_output"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Log output will appear here..."
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:background="@color/chip_background_unselected"
            android:padding="8dp" />

    </ScrollView>

</LinearLayout>
