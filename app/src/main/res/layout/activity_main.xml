<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_l"
    android:background="?attr/grey"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center"
        android:orientation="horizontal"
        android:id="@+id/update_view"
        android:background="?attr/colorr"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <TextView
            android:textSize="14sp"
            android:textColor="?attr/white"
            android:id="@+id/update_view_text"
            android:paddingTop="12.5dp"
            android:paddingBottom="12.5dp"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/update_downloading"
            android:singleLine="true"
            android:layout_weight="1"
            android:textAlignment="viewStart"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"/>
        <TextView
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:id="@+id/update_view_btn"
            android:background="@drawable/grey_block"
            android:paddingTop="12.5dp"
            android:paddingBottom="12.5dp"
            android:focusable="true"
            android:visibility="invisible"
            android:clickable="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:text="@string/update_install"
            android:singleLine="true"
            android:textAlignment="center"
            android:paddingStart="17dp"
            android:paddingEnd="17dp"
            android:layout_marginEnd="4dp"/>
    </LinearLayout>
    <FrameLayout
        android:id="@+id/nav_host_fragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/update_view"
        app:layout_constraintBottom_toTopOf="@+id/ads_baner_layout"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ads_baner_layout"
        android:visibility="visible"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="7dp"
        app:layout_constraintBottom_toTopOf="@+id/panel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/panel"
        android:background="@drawable/back_panel_test_grey"
        android:visibility="visible"
        android:layout_width="wrap_content"
        android:layout_height="75dp"
        android:layout_marginBottom="7dp"
        android:layout_marginStart="7dp"
        android:layout_marginEnd="7dp"
        android:elevation="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_view"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            app:menu="@menu/main_menu"/>
    </LinearLayout>
<!--    <com.applovin.mediation.ads.MaxAdView-->
<!--        xmlns:maxads="http://schemas.applovin.com/android/1.0"-->
<!--        android:id="@+id/banner_container"-->
<!--        maxads:adUnitId="d25a1beaccf7bd55"-->
<!--        android:background="?attr/grey"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="@dimen/_50sdp"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"/>-->
</androidx.constraintlayout.widget.ConstraintLayout>
